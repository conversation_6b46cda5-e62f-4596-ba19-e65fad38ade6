# Contributing to Microgen

Thank you for your interest in contributing to Microgen! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- Node.js 16.x or higher
- npm 7.x or higher
- Git

### Development Setup

1. **Fork and clone the repository**
   ```bash
   git clone https://github.com/your-username/microgen.git
   cd microgen
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Build the project**
   ```bash
   npm run build
   ```

4. **Link for local testing**
   ```bash
   npm run link:local
   ```

5. **Test the CLI**
   ```bash
   microgen --help
   ```

## 🛠️ Development Workflow

### Available Scripts

- `npm run build` - Build the TypeScript project
- `npm run build:clean` - Clean build (removes dist folder first)
- `npm run dev` - Run in development mode with ts-node
- `npm run test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues automatically
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting
- `npm run typecheck` - Run TypeScript type checking
- `npm run validate` - Run all checks (typecheck, lint, test)

### Making Changes

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow the existing code style
   - Add tests for new functionality
   - Update documentation as needed

3. **Test your changes**
   ```bash
   npm run validate
   ```

4. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

5. **Push and create a pull request**
   ```bash
   git push origin feature/your-feature-name
   ```

## 📝 Code Style Guidelines

### TypeScript

- Use TypeScript for all new code
- Prefer interfaces over types for object shapes
- Use strict typing (avoid `any`)
- Add JSDoc comments for public APIs

### Formatting

- Use Prettier for code formatting
- 2 spaces for indentation
- Single quotes for strings
- Trailing commas in ES5 contexts

### Naming Conventions

- Use camelCase for variables and functions
- Use PascalCase for classes and interfaces
- Use UPPER_SNAKE_CASE for constants
- Use kebab-case for file names

## 🧪 Testing

### Writing Tests

- Write tests for all new functionality
- Use Jest as the testing framework
- Place tests in `src/__tests__/` directory
- Use descriptive test names

### Test Structure

```typescript
describe('Feature Name', () => {
  beforeEach(() => {
    // Setup
  });

  it('should do something specific', () => {
    // Test implementation
  });
});
```

## 📚 Documentation

### README Updates

- Update README.md for new features
- Include code examples
- Update installation instructions if needed

### Code Documentation

- Add JSDoc comments for public APIs
- Include parameter and return type descriptions
- Provide usage examples

## 🐛 Bug Reports

When reporting bugs, please include:

- Node.js version
- npm version
- Operating system
- Steps to reproduce
- Expected behavior
- Actual behavior
- Error messages (if any)

## 💡 Feature Requests

For feature requests, please provide:

- Clear description of the feature
- Use case and motivation
- Proposed implementation (if any)
- Examples of similar features in other tools

## 📋 Pull Request Guidelines

### Before Submitting

- [ ] Tests pass (`npm test`)
- [ ] Linting passes (`npm run lint`)
- [ ] TypeScript compiles (`npm run typecheck`)
- [ ] Code is formatted (`npm run format`)
- [ ] Documentation is updated
- [ ] CHANGELOG.md is updated (for significant changes)

### PR Description

Please include:

- Summary of changes
- Motivation and context
- Type of change (bug fix, feature, breaking change, etc.)
- Testing instructions
- Screenshots (if applicable)

## 🏷️ Commit Message Format

We follow the [Conventional Commits](https://conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Types

- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

### Examples

```
feat: add support for Vue.js frontend framework
fix: resolve template generation issue with special characters
docs: update installation instructions
```

## 🤝 Community

- Be respectful and inclusive
- Help others learn and grow
- Share knowledge and best practices
- Provide constructive feedback

## 📄 License

By contributing to Microgen, you agree that your contributions will be licensed under the MIT License.

## ❓ Questions?

If you have questions about contributing, please:

1. Check existing issues and discussions
2. Create a new discussion in the repository
3. Reach out to maintainers

Thank you for contributing to Microgen! 🎉
