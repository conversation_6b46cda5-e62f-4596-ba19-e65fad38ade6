# Microgen

A modern CLI tool for generating microservice projects with frontend and backend technologies. Built with TypeScript and designed for rapid development of scalable microservice architectures.

## Features

### 🚀 Backend Technologies
- **NestJS** (Recommended) - Enterprise-grade Node.js framework with microservice support
- **Express.js** - Minimal and flexible Node.js web application framework

### 🌐 Frontend Technologies
- **Next.js** (Recommended) - React framework with SSR/SSG capabilities
- **React** with Vite - Fast development with hot module replacement
- **Vue.js** - Progressive JavaScript framework
- **Angular** - Platform for building mobile and desktop web applications
- **Svelte** - Cybernetically enhanced web apps

### 🔄 Microservice Communication
- **Kafka** - Distributed event streaming platform
- **gRPC** - High-performance, open-source universal RPC framework
- **Redis** - In-memory data structure store for pub/sub
- **TCP** - Raw TCP transport for direct communication
- **NATS** - Cloud native messaging system
- **RabbitMQ** - Message broker for reliable messaging

### 🗄️ Database Support
- **PostgreSQL** - Advanced open source relational database
- **MySQL** - Popular relational database management system
- **MongoDB** - Document-oriented NoSQL database
- **SQLite** - Lightweight, file-based SQL database

### 🎨 Styling Options
- **Tailwind CSS** - Utility-first CSS framework
- **Styled Components** - CSS-in-JS library for React
- **CSS Modules** - Localized CSS
- **Sass/SCSS** - CSS extension language

### 📦 Additional Features
- TypeScript support for all generated code
- Docker containerization with multi-stage builds
- Docker Compose for local development
- JWT Authentication (optional)
- Swagger/OpenAPI documentation
- Health check endpoints
- CORS configuration
- Environment-based configuration
- Message pattern decorators for microservices
- State management (Redux, Zustand, Context API)

## Installation

```bash
npm install -g microgen
```

Or use directly with npx:
```bash
npx microgen create my-project
```

## Quick Start

### Create a new microservice project
```bash
microgen create my-microservice
```

### Create with specific options (non-interactive)
```bash
microgen create my-project --template full --output-dir ./projects
```

### Dry run to see what would be generated
```bash
microgen create my-project --dry-run
```

## Usage

### Interactive Mode (Default)
The CLI will guide you through a series of questions to configure your project:

1. **Project Configuration**
   - Project name, description, author
   - Version and license

2. **Backend Services**
   - Service name and type (API, Web, Worker, Gateway)
   - Framework choice (NestJS or Express.js)
   - Database selection
   - Authentication and CORS settings
   - Microservice transport methods
   - Message pattern support

3. **Frontend Application**
   - Framework selection
   - Styling framework
   - State management
   - API configuration

### Command Options

```bash
microgen create <project-name> [options]

Options:
  -o, --output-dir <dir>     Output directory (default: current directory)
  -f, --force               Overwrite existing files
  -i, --interactive         Interactive mode (default: true)
  -t, --template <template>  Project template (full, api-only, frontend-only)
  -v, --verbose             Verbose output
  --dry-run                 Show what would be generated without creating files
  -h, --help                Display help information
```

## Project Structure

Generated projects follow a clean, organized structure:

```
my-microservice/
├── services/
│   └── api/                 # NestJS/Express backend service
│       ├── src/
│       │   ├── main.ts
│       │   ├── app.module.ts
│       │   ├── microservice/
│       │   ├── auth/        # Authentication (if enabled)
│       │   ├── database/    # Database configuration
│       │   └── config/
│       ├── package.json
│       ├── Dockerfile
│       └── .env.example
├── frontend/                # Next.js/React frontend
│   ├── app/
│   ├── lib/
│   ├── components/
│   ├── package.json
│   └── Dockerfile
├── docker-compose.yml       # Local development setup
├── package.json            # Root package.json with scripts
└── README.md               # Project documentation
```

## Examples

### NestJS API with Kafka and gRPC
```bash
microgen create ecommerce-api
# Select NestJS, PostgreSQL, Kafka + gRPC transports
```

### Next.js Frontend with Tailwind
```bash
microgen create ecommerce-frontend
# Select frontend-only template, Next.js, Tailwind CSS
```

### Full Stack Microservice
```bash
microgen create ecommerce-platform
# Select both backend (NestJS) and frontend (Next.js)
# Configure multiple transport methods and authentication
```

## Development

### Local Development
```bash
# Install dependencies
npm install

# Build the CLI
npm run build

# Test locally
npm run dev create test-project --dry-run

# Run tests
npm test
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Templates

The CLI includes several built-in templates:

- **Express.js API** - RESTful API with Express.js
- **NestJS Microservice** - Enterprise-grade microservice with NestJS
- **React Frontend** - Modern React application with Vite
- **Next.js Application** - Full-stack React application with SSR/SSG
- **Docker Configuration** - Multi-stage Docker builds and compose files

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Support

- 📖 [Documentation](https://github.com/microgen-cli/microgen/docs)
- 🐛 [Issue Tracker](https://github.com/microgen-cli/microgen/issues)
- 💬 [Discussions](https://github.com/microgen-cli/microgen/discussions)
