syntax = "proto3";

package {{service.name}};

service {{pascalCase service.name}}Service {
  rpc GetInfo(GetInfoRequest) returns (ServiceInfo);
  rpc ProcessData(ProcessDataRequest) returns (ProcessDataResponse);
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);
}

message GetInfoRequest {
  string id = 1;
}

message ServiceInfo {
  string id = 1;
  string name = 2;
  string version = 3;
  string status = 4;
  string timestamp = 5;
}

message ProcessDataRequest {
  string id = 1;
  string data = 2;
  map<string, string> metadata = 3;
}

message ProcessDataResponse {
  string id = 1;
  string result = 2;
  bool success = 3;
  string message = 4;
}

message HealthCheckRequest {}

message HealthCheckResponse {
  string status = 1;
  string timestamp = 2;
  map<string, string> details = 3;
}
