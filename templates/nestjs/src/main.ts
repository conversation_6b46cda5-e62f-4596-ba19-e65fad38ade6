import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module.js';
{{#service.microserviceTransport}}
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { join } from 'path';
{{/service.microserviceTransport}}

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // CORS configuration
  {{#service.cors}}
  app.enableCors({
    origin: process.env.CORS_ORIGIN || '*',
    credentials: true,
  });
  {{/service.cors}}

  // Global prefix
  app.setGlobalPrefix('api');

  {{#service.swagger}}
  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('{{service.name}} API')
    .setDescription('{{service.name}} microservice API documentation')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document);
  {{/service.swagger}}

  {{#service.microserviceTransport}}
  // Microservice configuration
  {{#each service.microserviceTransport}}
  {{#if (eq this "kafka")}}
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.KAFKA,
    options: {
      client: {
        clientId: '{{../service.name}}-service',
        brokers: [process.env.KAFKA_BROKER || 'localhost:9092'],
      },
      consumer: {
        groupId: '{{../service.name}}-consumer',
      },
    },
  });
  {{/if}}
  {{#if (eq this "grpc")}}
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.GRPC,
    options: {
      package: '{{../service.name}}',
      protoPath: join(__dirname, 'proto/service.proto'),
      url: process.env.GRPC_URL || '0.0.0.0:50051',
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
      },
    },
  });
  {{/if}}
  {{#if (eq this "redis")}}
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.REDIS,
    options: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
    },
  });
  {{/if}}
  {{#if (eq this "tcp")}}
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.TCP,
    options: {
      host: process.env.TCP_HOST || 'localhost',
      port: parseInt(process.env.TCP_PORT || '3001'),
    },
  });
  {{/if}}
  {{#if (eq this "nats")}}
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.NATS,
    options: {
      servers: [process.env.NATS_URL || 'nats://localhost:4222'],
    },
  });
  {{/if}}
  {{#if (eq this "rabbitmq")}}
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      urls: [process.env.RABBITMQ_URL || 'amqp://localhost:5672'],
      queue: '{{../service.name}}_queue',
      queueOptions: {
        durable: false,
      },
    },
  });
  {{/if}}
  {{/each}}

  await app.startAllMicroservices();
  {{/service.microserviceTransport}}

  const port = process.env.PORT || {{service.port}};
  await app.listen(port);
  
  console.log(`🚀 {{service.name}} service running on port ${port}`);
  {{#service.swagger}}
  console.log(`📚 API documentation available at http://localhost:${port}/docs`);
  {{/service.swagger}}
}

bootstrap().catch(err => {
  console.error('Error starting application:', err);
  process.exit(1);
});
