import { Module } from '@nestjs/common';
import { MicroserviceController } from './microservice.controller.js';
import { MicroserviceService } from './microservice.service.js';
import { MicroserviceClientModule } from './clients/microservice-client.module.js';

@Module({
  imports: [MicroserviceClientModule],
  controllers: [MicroserviceController],
  providers: [MicroserviceService],
  exports: [MicroserviceService, MicroserviceClientModule],
})
export class MicroserviceModule {}
