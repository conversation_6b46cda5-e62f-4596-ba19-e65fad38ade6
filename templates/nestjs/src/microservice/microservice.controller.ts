import { <PERSON>, Logger } from '@nestjs/common';
import { MessagePattern, EventPattern, Payload, GrpcMethod } from '@nestjs/microservices';
import { MicroserviceService } from './microservice.service.js';

// Type definitions for better type safety
interface ServiceInfoRequest {
  id?: string;
}

interface ServiceInfoResponse {
  id: string;
  name: string;
  version: string;
  status: string;
  timestamp: string;
}

interface ProcessDataRequest {
  id: string;
  data: any;
  metadata?: Record<string, string>;
}

interface ProcessDataResponse {
  id: string;
  result: any;
  success: boolean;
  message?: string;
}

@Controller()
export class MicroserviceController {
  private readonly logger = new Logger(MicroserviceController.name);

  constructor(private readonly microserviceService: MicroserviceService) {}

  {{#service.messagePatterns}}
  // Kafka/Redis Message Patterns
  @MessagePattern('{{service.name}}.get.info')
  async getServiceInfo(@Payload() data: ServiceInfoRequest): Promise<ServiceInfoResponse> {
    this.logger.log(`Received get info request: ${JSON.stringify(data)}`);
    return this.microserviceService.getServiceInfo(data);
  }

  @MessagePattern('{{service.name}}.process.data')
  async processData(@Payload() data: ProcessDataRequest): Promise<ProcessDataResponse> {
    this.logger.log(`Received process data request: ${JSON.stringify(data)}`);
    return this.microserviceService.processData(data);
  }

  // Event Patterns (Fire and forget)
  @EventPattern('{{service.name}}.event.created')
  async handleCreatedEvent(@Payload() data: any): Promise<void> {
    this.logger.log(`Received created event: ${JSON.stringify(data)}`);
    await this.microserviceService.handleCreatedEvent(data);
  }

  @EventPattern('{{service.name}}.event.updated')
  async handleUpdatedEvent(@Payload() data: any): Promise<void> {
    this.logger.log(`Received updated event: ${JSON.stringify(data)}`);
    await this.microserviceService.handleUpdatedEvent(data);
  }

  @EventPattern('{{service.name}}.event.deleted')
  async handleDeletedEvent(@Payload() data: any): Promise<void> {
    this.logger.log(`Received deleted event: ${JSON.stringify(data)}`);
    await this.microserviceService.handleDeletedEvent(data);
  }
  {{/service.messagePatterns}}

  {{#if (includes service.microserviceTransport "grpc")}}
  // gRPC Methods
  @GrpcMethod('{{pascalCase service.name}}Service', 'GetInfo')
  async getInfoGrpc(data: ServiceInfoRequest): Promise<ServiceInfoResponse> {
    this.logger.log(`gRPC GetInfo called with: ${JSON.stringify(data)}`);
    return this.microserviceService.getServiceInfo(data);
  }

  @GrpcMethod('{{pascalCase service.name}}Service', 'ProcessData')
  async processDataGrpc(data: ProcessDataRequest): Promise<ProcessDataResponse> {
    this.logger.log(`gRPC ProcessData called with: ${JSON.stringify(data)}`);
    return this.microserviceService.processData(data);
  }

  @GrpcMethod('{{pascalCase service.name}}Service', 'HealthCheck')
  async healthCheckGrpc(): Promise<{ status: string; timestamp: string }> {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
    };
  }
  {{/if}}
}
