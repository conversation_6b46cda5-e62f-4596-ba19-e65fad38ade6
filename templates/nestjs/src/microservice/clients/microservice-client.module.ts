import { Modu<PERSON> } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { join } from 'path';
{{#if (includes service.microserviceTransport "kafka")}}
import { KafkaClientService } from './kafka-client.service.js';
{{/if}}
{{#if (includes service.microserviceTransport "grpc")}}
import { GrpcClientService } from './grpc-client.service.js';
{{/if}}
{{#if (includes service.microserviceTransport "redis")}}
import { RedisClientService } from './redis-client.service.js';
{{/if}}

@Module({
  imports: [
    ClientsModule.registerAsync([
      {{#if (includes service.microserviceTransport "kafka")}}
      {
        name: 'KAFKA_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.KAFKA,
          options: {
            client: {
              clientId: configService.get('microservices.kafka.clientId'),
              brokers: configService.get('microservices.kafka.brokers'),
            },
            consumer: {
              groupId: configService.get('microservices.kafka.groupId'),
            },
          },
        }),
        inject: [ConfigService],
      },
      {{/if}}
      {{#if (includes service.microserviceTransport "grpc")}}
      {
        name: 'GRPC_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.GRPC,
          options: {
            package: configService.get('microservices.grpc.package'),
            protoPath: join(__dirname, '../proto/service.proto'),
            url: configService.get('microservices.grpc.url'),
            loader: {
              keepCase: true,
              longs: String,
              enums: String,
              defaults: true,
              oneofs: true,
            },
          },
        }),
        inject: [ConfigService],
      },
      {{/if}}
      {{#if (includes service.microserviceTransport "redis")}}
      {
        name: 'REDIS_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.REDIS,
          options: {
            host: configService.get('microservices.redis.host'),
            port: configService.get('microservices.redis.port'),
            retryAttempts: 5,
            retryDelay: 3000,
          },
        }),
        inject: [ConfigService],
      },
      {{/if}}
    ]),
  ],
  providers: [
    {{#if (includes service.microserviceTransport "kafka")}}
    KafkaClientService,
    {{/if}}
    {{#if (includes service.microserviceTransport "grpc")}}
    GrpcClientService,
    {{/if}}
    {{#if (includes service.microserviceTransport "redis")}}
    RedisClientService,
    {{/if}}
  ],
  exports: [
    {{#if (includes service.microserviceTransport "kafka")}}
    KafkaClientService,
    {{/if}}
    {{#if (includes service.microserviceTransport "grpc")}}
    GrpcClientService,
    {{/if}}
    {{#if (includes service.microserviceTransport "redis")}}
    RedisClientService,
    {{/if}}
  ],
})
export class MicroserviceClientModule {}
