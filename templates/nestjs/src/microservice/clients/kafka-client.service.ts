import { Injectable, Inject, OnModuleInit, OnModuleD<PERSON>roy, Logger } from '@nestjs/common';
import { ClientKafka } from '@nestjs/microservices';
import { Observable, firstValueFrom, timeout, catchError } from 'rxjs';

export interface KafkaMessage<T = any> {
  pattern: string;
  data: T;
}

export interface KafkaResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

@Injectable()
export class KafkaClientService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(KafkaClientService.name);

  constructor(
    @Inject('KAFKA_SERVICE') private readonly kafkaClient: ClientKafka,
  ) {}

  async onModuleInit() {
    try {
      await this.kafkaClient.connect();
      this.logger.log('Kafka client connected successfully');
    } catch (error) {
      this.logger.error('Failed to connect to Kafka:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    try {
      await this.kafkaClient.close();
      this.logger.log('Kafka client disconnected');
    } catch (error) {
      this.logger.error('Error disconnecting Kafka client:', error);
    }
  }

  /**
   * Send a message and wait for response (Request-Response pattern)
   */
  async sendMessage<T, R>(pattern: string, data: T, timeoutMs: number = 5000): Promise<R> {
    try {
      this.logger.log(`Sending message to pattern: ${pattern}`);
      
      const response$ = this.kafkaClient.send<R, T>(pattern, data).pipe(
        timeout(timeoutMs),
        catchError((error) => {
          this.logger.error(`Error sending message to ${pattern}:`, error);
          throw error;
        })
      );

      const result = await firstValueFrom(response$);
      this.logger.log(`Received response from pattern: ${pattern}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to send message to ${pattern}:`, error);
      throw error;
    }
  }

  /**
   * Emit an event (Fire and forget pattern)
   */
  emitEvent<T>(pattern: string, data: T): Observable<any> {
    try {
      this.logger.log(`Emitting event to pattern: ${pattern}`);
      return this.kafkaClient.emit<any, T>(pattern, data);
    } catch (error) {
      this.logger.error(`Failed to emit event to ${pattern}:`, error);
      throw error;
    }
  }

  /**
   * Service-specific methods
   */

  // Get service information
  async getServiceInfo(serviceId?: string): Promise<KafkaResponse> {
    return this.sendMessage('{{service.name}}.get.info', { id: serviceId });
  }

  // Process data
  async processData(data: any, metadata?: Record<string, string>): Promise<KafkaResponse> {
    return this.sendMessage('{{service.name}}.process.data', {
      id: this.generateId(),
      data,
      metadata,
    });
  }

  // Emit events
  emitCreatedEvent(data: any): Observable<any> {
    return this.emitEvent('{{service.name}}.event.created', {
      ...data,
      timestamp: new Date().toISOString(),
    });
  }

  emitUpdatedEvent(data: any): Observable<any> {
    return this.emitEvent('{{service.name}}.event.updated', {
      ...data,
      timestamp: new Date().toISOString(),
    });
  }

  emitDeletedEvent(data: any): Observable<any> {
    return this.emitEvent('{{service.name}}.event.deleted', {
      ...data,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<KafkaResponse> {
    try {
      return await this.sendMessage('{{service.name}}.health.check', {}, 3000);
    } catch (error) {
      return {
        success: false,
        error: 'Service unavailable',
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Utility methods
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Batch operations
   */
  async sendBatch<T, R>(messages: KafkaMessage<T>[]): Promise<R[]> {
    const promises = messages.map(msg => 
      this.sendMessage<T, R>(msg.pattern, msg.data)
    );
    
    try {
      return await Promise.all(promises);
    } catch (error) {
      this.logger.error('Batch operation failed:', error);
      throw error;
    }
  }

  /**
   * Get client statistics
   */
  getClientStats() {
    return {
      connected: true, // You can implement actual connection status check
      lastActivity: new Date().toISOString(),
      service: '{{service.name}}',
    };
  }
}
