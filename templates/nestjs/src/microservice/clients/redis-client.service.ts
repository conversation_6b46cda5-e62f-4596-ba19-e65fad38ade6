import { Injectable, Inject, OnModuleInit, OnModule<PERSON><PERSON>roy, Logger } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { Observable, firstValueFrom, timeout, catchError } from 'rxjs';

export interface RedisMessage<T = any> {
  pattern: string;
  data: T;
}

export interface RedisResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

@Injectable()
export class RedisClientService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisClientService.name);

  constructor(
    @Inject('REDIS_SERVICE') private readonly redisClient: ClientProxy,
  ) {}

  async onModuleInit() {
    try {
      await this.redisClient.connect();
      this.logger.log('Redis client connected successfully');
    } catch (error) {
      this.logger.error('Failed to connect to Redis:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    try {
      await this.redisClient.close();
      this.logger.log('Redis client disconnected');
    } catch (error) {
      this.logger.error('Error disconnecting Redis client:', error);
    }
  }

  /**
   * Send a message and wait for response (Request-Response pattern)
   */
  async sendMessage<T, R>(pattern: string, data: T, timeoutMs: number = 5000): Promise<R> {
    try {
      this.logger.log(`Sending message to pattern: ${pattern}`);
      
      const response$ = this.redisClient.send<R, T>(pattern, data).pipe(
        timeout(timeoutMs),
        catchError((error) => {
          this.logger.error(`Error sending message to ${pattern}:`, error);
          throw error;
        })
      );

      const result = await firstValueFrom(response$);
      this.logger.log(`Received response from pattern: ${pattern}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to send message to ${pattern}:`, error);
      throw error;
    }
  }

  /**
   * Emit an event (Fire and forget pattern)
   */
  emitEvent<T>(pattern: string, data: T): Observable<any> {
    try {
      this.logger.log(`Emitting event to pattern: ${pattern}`);
      return this.redisClient.emit<any, T>(pattern, data);
    } catch (error) {
      this.logger.error(`Failed to emit event to ${pattern}:`, error);
      throw error;
    }
  }

  /**
   * Service-specific methods
   */

  // Get service information
  async getServiceInfo(serviceId?: string): Promise<RedisResponse> {
    return this.sendMessage('{{service.name}}.get.info', { id: serviceId });
  }

  // Process data
  async processData(data: any, metadata?: Record<string, string>): Promise<RedisResponse> {
    return this.sendMessage('{{service.name}}.process.data', {
      id: this.generateId(),
      data,
      metadata,
    });
  }

  // Emit events
  emitCreatedEvent(data: any): Observable<any> {
    return this.emitEvent('{{service.name}}.event.created', {
      ...data,
      timestamp: new Date().toISOString(),
    });
  }

  emitUpdatedEvent(data: any): Observable<any> {
    return this.emitEvent('{{service.name}}.event.updated', {
      ...data,
      timestamp: new Date().toISOString(),
    });
  }

  emitDeletedEvent(data: any): Observable<any> {
    return this.emitEvent('{{service.name}}.event.deleted', {
      ...data,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<RedisResponse> {
    try {
      return await this.sendMessage('{{service.name}}.health.check', {}, 3000);
    } catch (error) {
      return {
        success: false,
        error: 'Service unavailable',
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Cache operations (Redis-specific)
   */
  async setCache<T>(key: string, value: T, ttl?: number): Promise<RedisResponse> {
    return this.sendMessage('cache.set', { key, value, ttl });
  }

  async getCache<T>(key: string): Promise<RedisResponse<T>> {
    return this.sendMessage('cache.get', { key });
  }

  async deleteCache(key: string): Promise<RedisResponse> {
    return this.sendMessage('cache.delete', { key });
  }

  async clearCache(pattern?: string): Promise<RedisResponse> {
    return this.sendMessage('cache.clear', { pattern });
  }

  /**
   * Pub/Sub operations
   */
  subscribeToChannel(channel: string): Observable<any> {
    this.logger.log(`Subscribing to channel: ${channel}`);
    return this.redisClient.send(`subscribe.${channel}`, {});
  }

  publishToChannel<T>(channel: string, data: T): Observable<any> {
    this.logger.log(`Publishing to channel: ${channel}`);
    return this.emitEvent(`publish.${channel}`, data);
  }

  /**
   * Session management (Redis-specific)
   */
  async createSession(sessionId: string, data: any, ttl: number = 3600): Promise<RedisResponse> {
    return this.sendMessage('session.create', { sessionId, data, ttl });
  }

  async getSession(sessionId: string): Promise<RedisResponse> {
    return this.sendMessage('session.get', { sessionId });
  }

  async updateSession(sessionId: string, data: any): Promise<RedisResponse> {
    return this.sendMessage('session.update', { sessionId, data });
  }

  async deleteSession(sessionId: string): Promise<RedisResponse> {
    return this.sendMessage('session.delete', { sessionId });
  }

  /**
   * Rate limiting (Redis-specific)
   */
  async checkRateLimit(key: string, limit: number, window: number): Promise<RedisResponse<{ allowed: boolean; remaining: number; resetTime: number }>> {
    return this.sendMessage('ratelimit.check', { key, limit, window });
  }

  /**
   * Distributed locking (Redis-specific)
   */
  async acquireLock(lockKey: string, ttl: number = 30000): Promise<RedisResponse<{ lockId: string }>> {
    return this.sendMessage('lock.acquire', { lockKey, ttl });
  }

  async releaseLock(lockKey: string, lockId: string): Promise<RedisResponse> {
    return this.sendMessage('lock.release', { lockKey, lockId });
  }

  /**
   * Utility methods
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Batch operations
   */
  async sendBatch<T, R>(messages: RedisMessage<T>[]): Promise<R[]> {
    const promises = messages.map(msg => 
      this.sendMessage<T, R>(msg.pattern, msg.data)
    );
    
    try {
      return await Promise.all(promises);
    } catch (error) {
      this.logger.error('Batch operation failed:', error);
      throw error;
    }
  }

  /**
   * Connection status
   */
  getConnectionStatus(): { connected: boolean; service: string; timestamp: string } {
    return {
      connected: true, // You can implement actual connection status check
      service: '{{service.name}}',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Performance monitoring
   */
  async getStats(): Promise<RedisResponse<{ connections: number; memory: string; uptime: number }>> {
    return this.sendMessage('stats.get', {});
  }
}
