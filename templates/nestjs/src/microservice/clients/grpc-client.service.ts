import { Injectable, Inject, OnModuleInit, Logger } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { Observable, firstValueFrom, timeout, catchError } from 'rxjs';

// gRPC service interface (matches the proto definition)
interface {{pascalCase service.name}}GrpcService {
  getInfo(data: { id?: string }): Observable<any>;
  processData(data: { id: string; data: string; metadata?: Record<string, string> }): Observable<any>;
  healthCheck(data: {}): Observable<any>;
}

export interface GrpcServiceInfo {
  id: string;
  name: string;
  version: string;
  status: string;
  timestamp: string;
}

export interface GrpcProcessDataRequest {
  id: string;
  data: any;
  metadata?: Record<string, string>;
}

export interface GrpcProcessDataResponse {
  id: string;
  result: any;
  success: boolean;
  message?: string;
}

export interface GrpcHealthResponse {
  status: string;
  timestamp: string;
  details?: Record<string, string>;
}

@Injectable()
export class GrpcClientService implements OnModuleInit {
  private readonly logger = new Logger(GrpcClientService.name);
  private grpcService: {{pascalCase service.name}}GrpcService;

  constructor(
    @Inject('GRPC_SERVICE') private readonly grpcClient: ClientGrpc,
  ) {}

  onModuleInit() {
    try {
      this.grpcService = this.grpcClient.getService<{{pascalCase service.name}}GrpcService>('{{pascalCase service.name}}Service');
      this.logger.log('gRPC client service initialized');
    } catch (error) {
      this.logger.error('Failed to initialize gRPC service:', error);
      throw error;
    }
  }

  /**
   * Get service information
   */
  async getServiceInfo(id?: string): Promise<GrpcServiceInfo> {
    try {
      this.logger.log(`Getting service info${id ? ` for ID: ${id}` : ''}`);
      
      const response$ = this.grpcService.getInfo({ id }).pipe(
        timeout(5000),
        catchError((error) => {
          this.logger.error('Error getting service info:', error);
          throw error;
        })
      );

      const result = await firstValueFrom(response$);
      this.logger.log('Service info retrieved successfully');
      return result;
    } catch (error) {
      this.logger.error('Failed to get service info:', error);
      throw error;
    }
  }

  /**
   * Process data
   */
  async processData(request: GrpcProcessDataRequest): Promise<GrpcProcessDataResponse> {
    try {
      this.logger.log(`Processing data for ID: ${request.id}`);
      
      const grpcRequest = {
        id: request.id,
        data: JSON.stringify(request.data),
        metadata: request.metadata || {},
      };

      const response$ = this.grpcService.processData(grpcRequest).pipe(
        timeout(10000),
        catchError((error) => {
          this.logger.error('Error processing data:', error);
          throw error;
        })
      );

      const result = await firstValueFrom(response$);
      this.logger.log(`Data processed successfully for ID: ${request.id}`);
      
      return {
        ...result,
        result: result.result ? JSON.parse(result.result) : null,
      };
    } catch (error) {
      this.logger.error('Failed to process data:', error);
      throw error;
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<GrpcHealthResponse> {
    try {
      this.logger.log('Performing gRPC health check');
      
      const response$ = this.grpcService.healthCheck({}).pipe(
        timeout(3000),
        catchError((error) => {
          this.logger.error('gRPC health check failed:', error);
          return [{
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            details: { error: error.message },
          }];
        })
      );

      const result = await firstValueFrom(response$);
      this.logger.log('gRPC health check completed');
      return result;
    } catch (error) {
      this.logger.error('Health check error:', error);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        details: { error: error.message },
      };
    }
  }

  /**
   * Batch operations
   */
  async batchProcessData(requests: GrpcProcessDataRequest[]): Promise<GrpcProcessDataResponse[]> {
    try {
      this.logger.log(`Processing batch of ${requests.length} requests`);
      
      const promises = requests.map(request => this.processData(request));
      const results = await Promise.allSettled(promises);
      
      const responses: GrpcProcessDataResponse[] = [];
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          responses.push(result.value);
        } else {
          this.logger.error(`Batch request ${index} failed:`, result.reason);
          responses.push({
            id: requests[index].id,
            result: null,
            success: false,
            message: result.reason.message || 'Unknown error',
          });
        }
      });

      this.logger.log(`Batch processing completed: ${responses.filter(r => r.success).length}/${requests.length} successful`);
      return responses;
    } catch (error) {
      this.logger.error('Batch processing failed:', error);
      throw error;
    }
  }

  /**
   * Stream processing (if your proto supports streaming)
   */
  processDataStream(requests: Observable<GrpcProcessDataRequest>): Observable<GrpcProcessDataResponse> {
    // This would be implemented if your proto definition includes streaming
    // For now, we'll provide a basic implementation
    return new Observable(subscriber => {
      requests.subscribe({
        next: async (request) => {
          try {
            const response = await this.processData(request);
            subscriber.next(response);
          } catch (error) {
            subscriber.error(error);
          }
        },
        error: (error) => subscriber.error(error),
        complete: () => subscriber.complete(),
      });
    });
  }

  /**
   * Utility methods
   */
  private generateRequestId(): string {
    return `grpc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get client connection status
   */
  getConnectionStatus(): { connected: boolean; service: string; timestamp: string } {
    return {
      connected: !!this.grpcService,
      service: '{{service.name}}',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Wrapper methods for common operations
   */
  async ping(): Promise<boolean> {
    try {
      const health = await this.healthCheck();
      return health.status === 'healthy';
    } catch {
      return false;
    }
  }

  async getServiceVersion(): Promise<string> {
    try {
      const info = await this.getServiceInfo();
      return info.version || '1.0.0';
    } catch {
      return 'unknown';
    }
  }
}
