export default () => ({
  port: parseInt(process.env.PORT, 10) || {{service.port}},
  nodeEnv: process.env.NODE_ENV || 'development',
  {{#service.database}}
  database: {
    {{#if (eq service.database "postgresql")}}
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 5432,
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || '{{service.name}}_db',
    {{/if}}
    {{#if (eq service.database "mysql")}}
    type: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 3306,
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || '{{service.name}}_db',
    {{/if}}
    {{#if (eq service.database "mongodb")}}
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/{{service.name}}_db',
    {{/if}}
    {{#if (eq service.database "sqlite")}}
    type: 'sqlite',
    database: process.env.DB_PATH || './{{service.name}}.db',
    {{/if}}
    synchronize: process.env.NODE_ENV === 'development',
    logging: process.env.NODE_ENV === 'development',
  },
  {{/service.database}}
  {{#service.authentication}}
  jwt: {
    secret: process.env.JWT_SECRET || 'your-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },
  {{/service.authentication}}
  {{#service.microserviceTransport}}
  microservices: {
    {{#each service.microserviceTransport}}
    {{#if (eq this "kafka")}}
    kafka: {
      clientId: '{{../service.name}}-service',
      brokers: [process.env.KAFKA_BROKER || 'localhost:9092'],
      groupId: '{{../service.name}}-consumer',
    },
    {{/if}}
    {{#if (eq this "grpc")}}
    grpc: {
      url: process.env.GRPC_URL || 'localhost:50051',
      package: '{{../service.name}}',
    },
    {{/if}}
    {{#if (eq this "redis")}}
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    },
    {{/if}}
    {{#if (eq this "tcp")}}
    tcp: {
      host: process.env.TCP_HOST || 'localhost',
      port: parseInt(process.env.TCP_PORT, 10) || 3001,
    },
    {{/if}}
    {{#if (eq this "nats")}}
    nats: {
      servers: [process.env.NATS_URL || 'nats://localhost:4222'],
    },
    {{/if}}
    {{#if (eq this "rabbitmq")}}
    rabbitmq: {
      urls: [process.env.RABBITMQ_URL || 'amqp://localhost:5672'],
      queue: '{{../service.name}}_queue',
    },
    {{/if}}
    {{/each}}
  },
  {{/service.microserviceTransport}}
});
