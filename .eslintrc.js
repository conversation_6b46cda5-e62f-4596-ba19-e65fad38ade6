module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint'],
  extends: [
    'eslint:recommended',
  ],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  rules: {
    'no-unused-vars': 'off',
    'no-undef': 'off',
    'prefer-const': 'error',
    'no-var': 'error',
    'no-console': 'off',
  },
  ignorePatterns: ['.eslintrc.js', 'jest.config.js', 'dist/', 'node_modules/', 'web/'],
};
