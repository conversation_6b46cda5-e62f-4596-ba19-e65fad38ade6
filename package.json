{"name": "microgen", "version": "1.0.0", "description": "A modern CLI tool for generating microservice projects with frontend and backend technologies", "main": "dist/index.js", "bin": {"microgen": "./dist/index.js", "mg": "./dist/index.js"}, "scripts": {"build": "tsc", "build:clean": "rimraf dist && npm run build", "dev": "ts-node src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write \"src/**/*.{ts,js,json}\"", "format:check": "prettier --check \"src/**/*.{ts,js,json}\"", "typecheck": "tsc --noEmit", "validate": "npm run typecheck && npm run lint && npm run test", "prepare": "npm run build:clean", "prepublishOnly": "npm run validate && npm run build:clean", "preversion": "npm run validate", "version": "npm run format && git add -A src", "postversion": "git push && git push --tags", "publish:patch": "npm version patch && npm publish", "publish:minor": "npm version minor && npm publish", "publish:major": "npm version major && npm publish", "link:local": "npm run build && npm link", "unlink:local": "npm unlink -g microgen"}, "keywords": ["microservice", "cli", "generator", "frontend", "backend", "typescript", "react", "<PERSON><PERSON><PERSON>", "nextjs", "vue", "angular", "svelte", "tailwind", "docker", "kafka", "grpc", "postgresql", "mongodb", "project-generator", "scaffolding", "boilerplate"], "author": "Microgen CLI <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/microgen-cli/microgen.git"}, "bugs": {"url": "https://github.com/microgen-cli/microgen/issues"}, "homepage": "https://github.com/microgen-cli/microgen#readme", "publishConfig": {"access": "public"}, "dependencies": {"commander": "^11.1.0", "inquirer": "^9.2.12", "chalk": "^5.3.0", "ora": "^7.0.1", "fs-extra": "^11.1.1", "mustache": "^4.2.0", "semver": "^7.5.4"}, "devDependencies": {"@types/node": "^20.8.0", "@types/inquirer": "^9.0.7", "@types/fs-extra": "^11.0.4", "@types/mustache": "^4.2.5", "@types/semver": "^7.5.4", "@types/jest": "^29.5.5", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "^8.50.0", "jest": "^29.7.0", "prettier": "^3.0.3", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=16.0.0"}, "files": ["dist", "templates", "README.md"]}