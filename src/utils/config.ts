import fs from 'fs-extra';
import path from 'path';
import os from 'os';

/**
 * Configuration management for Microgen CLI
 */

export interface MicrogenConfig {
  defaultAuthor?: string;
  defaultEmail?: string;
  defaultLicense?: string;
  defaultTemplate?: string;
  telemetry?: boolean;
  checkUpdates?: boolean;
}

export class ConfigManager {
  private configPath: string;
  private config: MicrogenConfig;

  constructor() {
    this.configPath = path.join(os.homedir(), '.microgen', 'config.json');
    this.config = {};
    this.loadConfig();
  }

  /**
   * Load configuration from file
   */
  private loadConfig(): void {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readJsonSync(this.configPath);
        this.config = { ...this.getDefaultConfig(), ...configData };
      } else {
        this.config = this.getDefaultConfig();
      }
    } catch (error) {
      // If config is corrupted, use defaults
      this.config = this.getDefaultConfig();
    }
  }

  /**
   * Save configuration to file
   */
  private saveConfig(): void {
    try {
      fs.ensureDirSync(path.dirname(this.configPath));
      fs.writeJsonSync(this.configPath, this.config, { spaces: 2 });
    } catch (error) {
      // Silently fail if we can't save config
    }
  }

  /**
   * Get default configuration
   */
  private getDefaultConfig(): MicrogenConfig {
    return {
      defaultLicense: 'MIT',
      defaultTemplate: 'full',
      telemetry: false,
      checkUpdates: true,
    };
  }

  /**
   * Get configuration value
   */
  get<K extends keyof MicrogenConfig>(key: K): MicrogenConfig[K] {
    return this.config[key];
  }

  /**
   * Set configuration value
   */
  set<K extends keyof MicrogenConfig>(key: K, value: MicrogenConfig[K]): void {
    this.config[key] = value;
    this.saveConfig();
  }

  /**
   * Get all configuration
   */
  getAll(): MicrogenConfig {
    return { ...this.config };
  }

  /**
   * Reset configuration to defaults
   */
  reset(): void {
    this.config = this.getDefaultConfig();
    this.saveConfig();
  }

  /**
   * Check if config file exists
   */
  exists(): boolean {
    return fs.existsSync(this.configPath);
  }

  /**
   * Get config file path
   */
  getConfigPath(): string {
    return this.configPath;
  }

  /**
   * Initialize config with user preferences
   */
  async initialize(): Promise<void> {
    const inquirer = await import('inquirer');
    
    const answers = await inquirer.default.prompt([
      {
        type: 'input',
        name: 'defaultAuthor',
        message: 'Default author name:',
        default: this.config.defaultAuthor,
      },
      {
        type: 'input',
        name: 'defaultEmail',
        message: 'Default email:',
        default: this.config.defaultEmail,
        validate: (input: string) => {
          if (!input) return true;
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          return emailRegex.test(input) || 'Please enter a valid email address';
        },
      },
      {
        type: 'list',
        name: 'defaultLicense',
        message: 'Default license:',
        choices: ['MIT', 'Apache-2.0', 'GPL-3.0', 'BSD-3-Clause', 'ISC', 'Unlicense'],
        default: this.config.defaultLicense,
      },
      {
        type: 'list',
        name: 'defaultTemplate',
        message: 'Default project template:',
        choices: [
          { name: 'Full Stack (Frontend + Backend)', value: 'full' },
          { name: 'API Only (Backend)', value: 'api-only' },
          { name: 'Frontend Only', value: 'frontend-only' },
        ],
        default: this.config.defaultTemplate,
      },
      {
        type: 'confirm',
        name: 'checkUpdates',
        message: 'Check for updates automatically?',
        default: this.config.checkUpdates,
      },
    ]);

    Object.assign(this.config, answers);
    this.saveConfig();
  }
}

// Singleton instance
export const config = new ConfigManager();
