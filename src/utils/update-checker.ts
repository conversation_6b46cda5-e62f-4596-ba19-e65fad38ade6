import semver from 'semver';
import { Logger } from './logger.js';
import { config } from './config.js';

const packageJson = require('../../package.json');

/**
 * Check for updates to Microgen CLI
 */
export class UpdateChecker {
  private logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  /**
   * Check for available updates
   */
  async checkForUpdates(silent = false): Promise<void> {
    if (!config.get('checkUpdates')) {
      return;
    }

    try {
      const response = await fetch('https://registry.npmjs.org/microgen/latest');
      if (!response.ok) {
        return; // Silently fail if we can't check for updates
      }

      const data = await response.json();
      const latestVersion = data.version;
      const currentVersion = packageJson.version;

      if (semver.gt(latestVersion, currentVersion)) {
        if (!silent) {
          this.showUpdateNotification(currentVersion, latestVersion);
        }
      }
    } catch (error) {
      // Silently fail - don't interrupt the user experience
    }
  }

  /**
   * Show update notification
   */
  private showUpdateNotification(currentVersion: string, latestVersion: string): void {
    this.logger.warn(`📦 Update available: ${currentVersion} → ${latestVersion}`);
    this.logger.info('🚀 Run the following command to update:');
    this.logger.info('   npm install -g microgen@latest');
    this.logger.info('');
  }

  /**
   * Get current version
   */
  getCurrentVersion(): string {
    return packageJson.version;
  }

  /**
   * Get latest version from npm
   */
  async getLatestVersion(): Promise<string | null> {
    try {
      const response = await fetch('https://registry.npmjs.org/microgen/latest');
      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      return data.version;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if current version is latest
   */
  async isLatestVersion(): Promise<boolean> {
    const latestVersion = await this.getLatestVersion();
    if (!latestVersion) {
      return true; // Assume latest if we can't check
    }

    return semver.gte(packageJson.version, latestVersion);
  }
}
