/**
 * Validation utilities for CLI inputs
 */

export class ValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class Validator {
  /**
   * Validate project name according to npm package naming rules
   */
  static validateProjectName(name: string): void {
    if (!name || !name.trim()) {
      throw new ValidationError('Project name is required');
    }

    const trimmedName = name.trim();

    // Check for valid npm package name format
    const validNameRegex = /^[a-z0-9]([a-z0-9-]*[a-z0-9])?$/;
    if (!validNameRegex.test(trimmedName)) {
      throw new ValidationError(
        'Project name must be lowercase, contain only letters, numbers, and hyphens, and cannot start or end with a hyphen',
        'projectName'
      );
    }

    // Check for reserved names
    const reservedNames = [
      'node_modules', 'package', 'npm', 'test', 'src', 'dist', 'build',
      'public', 'static', 'assets', 'lib', 'bin', 'config', 'scripts'
    ];
    if (reservedNames.includes(trimmedName.toLowerCase())) {
      throw new ValidationError(
        `Project name "${trimmedName}" is reserved and cannot be used`,
        'projectName'
      );
    }

    // Check length
    if (trimmedName.length > 214) {
      throw new ValidationError(
        'Project name must be less than 214 characters',
        'projectName'
      );
    }

    // Check for scoped package format (not supported in this context)
    if (trimmedName.startsWith('@')) {
      throw new ValidationError(
        'Scoped package names are not supported for project names',
        'projectName'
      );
    }
  }

  /**
   * Validate directory path
   */
  static validateDirectory(path: string): void {
    if (!path || !path.trim()) {
      throw new ValidationError('Directory path is required');
    }

    // Check for invalid characters
    const invalidChars = /[<>:"|?*]/;
    if (invalidChars.test(path)) {
      throw new ValidationError(
        'Directory path contains invalid characters',
        'directory'
      );
    }
  }

  /**
   * Validate email format
   */
  static validateEmail(email: string): boolean {
    if (!email) return true; // Email is optional
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate version format (semver)
   */
  static validateVersion(version: string): boolean {
    if (!version) return false;
    
    const semverRegex = /^\d+\.\d+\.\d+(-[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*)?(\+[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*)?$/;
    return semverRegex.test(version);
  }

  /**
   * Validate port number
   */
  static validatePort(port: number | string): boolean {
    const portNum = typeof port === 'string' ? parseInt(port, 10) : port;
    return !isNaN(portNum) && portNum > 0 && portNum <= 65535;
  }

  /**
   * Sanitize string input
   */
  static sanitizeString(input: string): string {
    return input.trim().replace(/[^\w\s-]/g, '');
  }

  /**
   * Check if string is empty or whitespace only
   */
  static isEmpty(value: string | undefined | null): boolean {
    return !value || !value.trim();
  }
}

/**
 * Inquirer.js validation functions
 */
export const inquirerValidators = {
  projectName: (input: string) => {
    try {
      Validator.validateProjectName(input);
      return true;
    } catch (error) {
      return error instanceof ValidationError ? error.message : 'Invalid project name';
    }
  },

  required: (input: string) => {
    return !Validator.isEmpty(input) || 'This field is required';
  },

  email: (input: string) => {
    if (Validator.isEmpty(input)) return true; // Optional
    return Validator.validateEmail(input) || 'Please enter a valid email address';
  },

  version: (input: string) => {
    return Validator.validateVersion(input) || 'Please enter a valid version (e.g., 1.0.0)';
  },

  port: (input: string) => {
    return Validator.validatePort(input) || 'Please enter a valid port number (1-65535)';
  }
};
