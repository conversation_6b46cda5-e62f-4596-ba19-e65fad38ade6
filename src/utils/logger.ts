import chalk from 'chalk';
import ora, { Ora } from 'ora';

/**
 * Logger utility for consistent CLI output
 */
export class Logger {
  private verbose: boolean;
  private spinner?: Ora | undefined;

  constructor(verbose = false) {
    this.verbose = verbose;
  }

  /**
   * Log an info message
   */
  info(message: string): void {
    console.log(chalk.blue('ℹ'), message);
  }

  /**
   * Log a success message
   */
  success(message: string): void {
    console.log(chalk.green('✓'), message);
  }

  /**
   * Log a warning message
   */
  warn(message: string): void {
    console.log(chalk.yellow('⚠'), message);
  }

  /**
   * Log an error message
   */
  error(message: string): void {
    console.log(chalk.red('✗'), message);
  }

  /**
   * Log a debug message (only if verbose mode is enabled)
   */
  debug(message: string): void {
    if (this.verbose) {
      console.log(chalk.gray('🐛'), chalk.gray(message));
    }
  }

  /**
   * Start a spinner with a message
   */
  startSpinner(message: string): void {
    this.spinner = ora(message).start();
  }

  /**
   * Update spinner text
   */
  updateSpinner(message: string): void {
    if (this.spinner) {
      this.spinner.text = message;
    }
  }

  /**
   * Stop spinner with success
   */
  succeedSpinner(message?: string): void {
    if (this.spinner) {
      this.spinner.succeed(message);
      this.spinner = undefined;
    }
  }

  /**
   * Stop spinner with failure
   */
  failSpinner(message?: string): void {
    if (this.spinner) {
      this.spinner.fail(message);
      this.spinner = undefined;
    }
  }

  /**
   * Stop spinner
   */
  stopSpinner(): void {
    if (this.spinner) {
      this.spinner.stop();
      this.spinner = undefined;
    }
  }

  /**
   * Log a title/header
   */
  title(message: string): void {
    console.log();
    console.log(chalk.bold.cyan(message));
    console.log(chalk.cyan('='.repeat(message.length)));
  }

  /**
   * Log a subtitle
   */
  subtitle(message: string): void {
    console.log();
    console.log(chalk.bold(message));
  }

  /**
   * Display welcome banner
   */
  banner(): void {
    console.log();
    console.log(chalk.bold.magenta('  ███╗   ███╗██╗ ██████╗██████╗  ██████╗  ██████╗ ███████╗███╗   ██╗'));
    console.log(chalk.bold.magenta('  ████╗ ████║██║██╔════╝██╔══██╗██╔═══██╗██╔════╝ ██╔════╝████╗  ██║'));
    console.log(chalk.bold.magenta('  ██╔████╔██║██║██║     ██████╔╝██║   ██║██║  ███╗█████╗  ██╔██╗ ██║'));
    console.log(chalk.bold.magenta('  ██║╚██╔╝██║██║██║     ██╔══██╗██║   ██║██║   ██║██╔══╝  ██║╚██╗██║'));
    console.log(chalk.bold.magenta('  ██║ ╚═╝ ██║██║╚██████╗██║  ██║╚██████╔╝╚██████╔╝███████╗██║ ╚████║'));
    console.log(chalk.bold.magenta('  ╚═╝     ╚═╝╚═╝ ╚═════╝╚═╝  ╚═╝ ╚═════╝  ╚═════╝ ╚══════╝╚═╝  ╚═══╝'));
    console.log();
    console.log(chalk.bold.cyan('  🚀 Modern CLI for generating microservice projects'));
    console.log(chalk.gray('  ✨ Frontend • Backend • Microservices • Docker • TypeScript'));
    console.log();
  }

  /**
   * Display completion message
   */
  completion(projectName: string): void {
    console.log();
    console.log(chalk.bold.green('🎉 Project created successfully!'));
    console.log();
    console.log(chalk.bold('📋 Next steps:'));
    console.log(chalk.cyan(`  📁 cd ${projectName}`));
    console.log(chalk.cyan('  📦 npm install'));
    console.log(chalk.cyan('  🚀 npm run dev'));
    console.log();
    console.log(chalk.bold('📚 Resources:'));
    console.log(chalk.blue('  📖 Documentation: https://github.com/microgen-cli/microgen'));
    console.log(chalk.blue('  🐛 Issues: https://github.com/microgen-cli/microgen/issues'));
    console.log(chalk.blue('  💬 Discussions: https://github.com/microgen-cli/microgen/discussions'));
    console.log();
  }
}
