#!/usr/bin/env node

import { Command } from 'commander';
import { CreateProjectCommand } from './commands/create-project.js';
import { Logger } from './utils/logger.js';
import { UpdateChecker } from './utils/update-checker.js';

const packageJson = require('../package.json');

/**
 * Main CLI application
 */
class MicrogenCLI {
  private program: Command;
  private logger: Logger;
  private updateChecker: UpdateChecker;

  constructor() {
    this.program = new Command();
    this.logger = new Logger();
    this.updateChecker = new UpdateChecker(this.logger);
    this.setupProgram();
    this.registerCommands();
  }

  private setupProgram(): void {
    this.program
      .name('microgen')
      .description('A modern CLI tool for generating microservice projects with frontend and backend technologies')
      .version(packageJson.version)
      .option('-v, --verbose', 'Enable verbose output', false);
  }

  private registerCommands(): void {
    // Register create project command
    const createCommand = new CreateProjectCommand();
    createCommand.register(this.program);

    // Add help command
    this.program
      .command('help')
      .description('Display help information')
      .action(() => {
        this.program.help();
      });
  }

  async run(): Promise<void> {
    try {
      // Check for updates (non-blocking)
      this.updateChecker.checkForUpdates(true).catch(() => {
        // Silently ignore update check failures
      });

      await this.program.parseAsync(process.argv);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(error.message);
      } else {
        this.logger.error('An unexpected error occurred');
      }
      process.exit(1);
    }
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the CLI
const cli = new MicrogenCLI();
cli.run().catch((error) => {
  console.error('Failed to run CLI:', error);
  process.exit(1);
});
