import { Command } from 'commander';
import { Logger } from '../utils/logger.js';
import { FileUtils } from '../utils/file-utils.js';

/**
 * Base class for all CLI commands
 */
export abstract class BaseCommand {
  protected logger: Logger;
  protected fileUtils: FileUtils;

  constructor(verbose = false) {
    this.logger = new Logger(verbose);
    this.fileUtils = new FileUtils(this.logger);
  }

  /**
   * Abstract method to be implemented by each command
   */
  abstract execute(...args: unknown[]): Promise<void>;

  /**
   * Register the command with the CLI program
   */
  abstract register(program: Command): void;

  /**
   * Validate common options
   */
  protected validateOptions(options: { outputDir?: string; force?: boolean }): void {
    if (options.outputDir && !options.outputDir.trim()) {
      throw new Error('Output directory cannot be empty');
    }
  }

  /**
   * Validate project name
   */
  protected validateProjectName(projectName: string): void {
    if (!projectName || !projectName.trim()) {
      throw new Error('Project name is required');
    }

    // Check for valid npm package name format
    const validNameRegex = /^[a-z0-9]([a-z0-9-]*[a-z0-9])?$/;
    if (!validNameRegex.test(projectName)) {
      throw new Error(
        'Project name must be lowercase, contain only letters, numbers, and hyphens, and cannot start or end with a hyphen'
      );
    }

    // Check for reserved names
    const reservedNames = ['node_modules', 'package', 'npm', 'test', 'src', 'dist', 'build'];
    if (reservedNames.includes(projectName.toLowerCase())) {
      throw new Error(`Project name "${projectName}" is reserved and cannot be used`);
    }

    // Check length
    if (projectName.length > 214) {
      throw new Error('Project name must be less than 214 characters');
    }
  }

  /**
   * Handle common errors
   */
  protected handleError(error: unknown): void {
    this.logger.stopSpinner();

    if (error instanceof Error) {
      this.logger.error(`❌ ${error.message}`);

      // Provide helpful suggestions for common errors
      if (error.message.includes('ENOENT')) {
        this.logger.info('💡 Make sure the specified directory exists and you have proper permissions');
      } else if (error.message.includes('EACCES')) {
        this.logger.info('💡 Permission denied. Try running with appropriate permissions');
      } else if (error.message.includes('EEXIST')) {
        this.logger.info('💡 File or directory already exists. Use --force to overwrite');
      }
    } else {
      this.logger.error('❌ An unexpected error occurred');
    }

    this.logger.info('\n🔗 For help and support:');
    this.logger.info('  📖 Documentation: https://github.com/microgen-cli/microgen');
    this.logger.info('  🐛 Report issues: https://github.com/microgen-cli/microgen/issues');

    process.exit(1);
  }

  /**
   * Confirm action with user
   */
  protected async confirmAction(message: string): Promise<boolean> {
    const inquirer = await import('inquirer');
    const { confirm } = await inquirer.default.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message,
        default: false,
      },
    ]);
    return confirm as boolean;
  }
}
