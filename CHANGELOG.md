# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-01-XX

### Added
- 🎉 Initial release of Microgen CLI
- 🚀 Interactive project generation with modern technologies
- 🏗️ Support for multiple backend frameworks:
  - NestJS with microservice support
  - Express.js for lightweight APIs
- 🌐 Frontend framework support:
  - Next.js with SSR/SSG capabilities
  - React with Vite
  - Vue.js
  - Angular
  - Svelte
- 🔄 Microservice communication options:
  - Apache Kafka for event streaming
  - gRPC for high-performance RPC
  - Redis for pub/sub messaging
  - TCP for direct communication
  - NATS for cloud-native messaging
  - RabbitMQ for reliable messaging
- 🗄️ Database support:
  - PostgreSQL
  - MySQL
  - MongoDB
  - SQLite
- 🎨 Styling frameworks:
  - Tailwind CSS v4 (default)
  - Styled Components
  - CSS Modules
  - Sass/SCSS
- 📦 Production-ready features:
  - TypeScript support throughout
  - Docker containerization
  - Docker Compose for development
  - JWT Authentication (optional)
  - Swagger/OpenAPI documentation
  - Health check endpoints
  - CORS configuration
  - Environment-based configuration
- 🛠️ Developer experience:
  - Interactive CLI with beautiful prompts
  - Dry-run mode for previewing changes
  - Verbose logging option
  - Template validation
  - Error handling with helpful suggestions
- 📚 Documentation:
  - Comprehensive README
  - Publishing guide
  - Docusaurus-based documentation site
- 🧪 Testing:
  - Jest configuration
  - Sample tests included
  - CI/CD pipeline with GitHub Actions

### Technical Details
- Built with TypeScript for type safety
- Commander.js for CLI framework
- Inquirer.js for interactive prompts
- Mustache.js for templating
- Chalk for colorful output
- Ora for loading spinners
- ESLint and Prettier for code quality
- Comprehensive error handling and validation
- Update checker for latest version notifications
- Configuration management for user preferences

### Breaking Changes
- Renamed from `microservice-cli` to `microgen`
- Updated all command names and references
- New repository structure and organization

[1.0.0]: https://github.com/microgen-cli/microgen/releases/tag/v1.0.0
