import type {SidebarsConfig} from '@docusaurus/plugin-content-docs';

// This runs in Node.js - Don't use client-side code here (browser APIs, JSX...)

/**
 * Creating a sidebar enables you to:
 - create an ordered group of docs
 - render a sidebar for each doc of that group
 - provide next/previous navigation

 The sidebars can be generated from the filesystem, or explicitly defined here.

 Create as many sidebars as you want.
 */
const sidebars: SidebarsConfig = {
  // Manual sidebar for better organization
  tutorialSidebar: [
    'intro',
    'installation',
    'quick-start',
    {
      type: 'category',
      label: 'CLI Commands',
      items: [
        'commands/create',
        'commands/options',
      ],
    },
    {
      type: 'category',
      label: 'Project Templates',
      items: [
        'templates/overview',
      ],
    },
    {
      type: 'category',
      label: 'Examples',
      items: [
        'examples/basic-api',
      ],
    },
    'contributing',
  ],
};

export default sidebars;
