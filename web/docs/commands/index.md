---
sidebar_position: 1
---

# CLI Commands

Microgen provides a comprehensive set of commands to help you generate and manage microservice projects. This section covers all available commands and their options.

## Available Commands

### `create` - Generate New Projects

The primary command for creating new microservice projects with various templates and configurations.

```bash
microgen create <project-name> [options]
```

**Key Features:**
- 🚀 Interactive project setup
- 📦 Multiple project templates
- 🎯 Framework selection (NestJS, Express, Next.js, React, Vue, Angular, Svelte)
- 🗄️ Database integration (PostgreSQL, MySQL, MongoDB, SQLite)
- 🔄 Microservice communication (Kafka, gRPC, Redis, TCP, NATS, RabbitMQ)
- 🎨 Styling options (Tailwind CSS, Styled Components, CSS Modules, Sass)
- 🐳 Docker containerization

[**Learn more about the create command →**](./create)

### Command Options

All commands support these global options:

- `-v, --verbose` - Enable verbose output for detailed logging
- `-h, --help` - Display help information for any command
- `--version` - Show the current version of Microgen

[**Explore all command options →**](./options)

## Quick Examples

### Create a Full-Stack Project
```bash
microgen create my-app
```

### Create API-Only Backend
```bash
microgen create my-api --template api-only
```

### Create Frontend-Only App
```bash
microgen create my-frontend --template frontend-only
```

### Non-Interactive Mode
```bash
microgen create my-project --no-interactive --template full
```

### Preview Without Creating Files
```bash
microgen create my-project --dry-run
```

## Getting Help

For detailed information about any command, use the `--help` flag:

```bash
microgen --help
microgen create --help
```

## Next Steps

- [**Create Command Reference**](./create) - Detailed documentation for the create command
- [**Command Options**](./options) - Complete list of all available options
- [**Quick Start Guide**](../quick-start) - Get started with your first project
- [**Examples**](../examples/basic-api) - Real-world usage examples
