---
sidebar_position: 5
---

# Backup and Recovery

Implement comprehensive backup and disaster recovery strategies for your Microgen applications.

## Overview

Data protection is critical for production applications. This guide covers backup strategies, disaster recovery planning, and data protection best practices.

## Database Backup

### PostgreSQL Backup

**Automated Backups:**
```bash
#!/bin/bash
# backup-postgres.sh

DB_NAME="myapp"
DB_USER="postgres"
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup
pg_dump -U $DB_USER -h localhost $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/backup_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete
```

**Docker Backup:**
```yaml
# docker-compose.yml
version: '3.8'
services:
  postgres-backup:
    image: postgres:13
    environment:
      - PGPASSWORD=password
    volumes:
      - ./backups:/backups
      - ./scripts:/scripts
    command: >
      bash -c "
        while true; do
          pg_dump -h postgres -U user myapp > /backups/backup_$(date +%Y%m%d_%H%M%S).sql
          sleep 86400
        done
      "
    depends_on:
      - postgres
```

### MongoDB Backup

```bash
#!/bin/bash
# backup-mongodb.sh

DB_NAME="myapp"
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup
mongodump --db $DB_NAME --out $BACKUP_DIR/mongodb_$DATE

# Compress backup
tar -czf $BACKUP_DIR/mongodb_$DATE.tar.gz $BACKUP_DIR/mongodb_$DATE
rm -rf $BACKUP_DIR/mongodb_$DATE

# Remove old backups
find $BACKUP_DIR -name "mongodb_*.tar.gz" -mtime +30 -delete
```

## Application Backup

### File System Backup

```bash
#!/bin/bash
# backup-files.sh

APP_DIR="/app"
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create application backup
tar -czf $BACKUP_DIR/app_$DATE.tar.gz \
  --exclude='node_modules' \
  --exclude='dist' \
  --exclude='logs' \
  $APP_DIR

# Upload to cloud storage
aws s3 cp $BACKUP_DIR/app_$DATE.tar.gz s3://my-backup-bucket/
```

### Configuration Backup

```typescript
// Configuration backup service
@Injectable()
export class BackupService {
  async backupConfiguration() {
    const config = {
      environment: process.env.NODE_ENV,
      database: {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        name: process.env.DB_NAME,
      },
      redis: {
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT,
      },
      // Don't backup secrets!
    };

    const timestamp = new Date().toISOString();
    const backupData = {
      timestamp,
      config,
      version: process.env.npm_package_version,
    };

    await this.uploadToStorage(`config-backup-${timestamp}.json`, backupData);
  }
}
```

## Cloud Backup

### AWS S3 Backup

```typescript
// S3 backup service
import { S3 } from 'aws-sdk';

@Injectable()
export class S3BackupService {
  private s3 = new S3();

  async uploadBackup(filename: string, data: Buffer) {
    const params = {
      Bucket: process.env.BACKUP_BUCKET,
      Key: `backups/${filename}`,
      Body: data,
      StorageClass: 'STANDARD_IA', // Infrequent Access
    };

    await this.s3.upload(params).promise();
  }

  async listBackups(prefix: string) {
    const params = {
      Bucket: process.env.BACKUP_BUCKET,
      Prefix: `backups/${prefix}`,
    };

    const result = await this.s3.listObjectsV2(params).promise();
    return result.Contents;
  }

  async downloadBackup(key: string) {
    const params = {
      Bucket: process.env.BACKUP_BUCKET,
      Key: key,
    };

    const result = await this.s3.getObject(params).promise();
    return result.Body;
  }
}
```

### Google Cloud Storage

```typescript
// GCS backup service
import { Storage } from '@google-cloud/storage';

@Injectable()
export class GCSBackupService {
  private storage = new Storage();
  private bucket = this.storage.bucket(process.env.BACKUP_BUCKET);

  async uploadBackup(filename: string, data: Buffer) {
    const file = this.bucket.file(`backups/${filename}`);
    
    await file.save(data, {
      metadata: {
        contentType: 'application/gzip',
      },
    });
  }
}
```

## Disaster Recovery

### Recovery Planning

```typescript
// Disaster recovery service
@Injectable()
export class DisasterRecoveryService {
  async createRecoveryPlan() {
    return {
      rto: '4 hours', // Recovery Time Objective
      rpo: '1 hour',  // Recovery Point Objective
      
      procedures: [
        'Assess damage and scope',
        'Activate backup systems',
        'Restore from latest backup',
        'Verify data integrity',
        'Resume operations',
        'Post-incident review',
      ],
      
      contacts: [
        { role: 'Incident Commander', phone: '+1234567890' },
        { role: 'Technical Lead', phone: '+1234567891' },
        { role: 'Database Admin', phone: '+1234567892' },
      ],
    };
  }
}
```

### Automated Recovery

```bash
#!/bin/bash
# disaster-recovery.sh

BACKUP_BUCKET="my-backup-bucket"
RECOVERY_DIR="/recovery"

echo "Starting disaster recovery process..."

# Download latest backup
aws s3 cp s3://$BACKUP_BUCKET/latest-backup.sql.gz $RECOVERY_DIR/

# Extract backup
gunzip $RECOVERY_DIR/latest-backup.sql.gz

# Restore database
psql -U postgres -d myapp < $RECOVERY_DIR/latest-backup.sql

# Restart services
docker-compose up -d

echo "Disaster recovery completed"
```

## Backup Monitoring

### Backup Verification

```typescript
// Backup verification service
@Injectable()
export class BackupVerificationService {
  @Cron('0 2 * * *') // Daily at 2 AM
  async verifyBackups() {
    const backups = await this.listRecentBackups();
    
    for (const backup of backups) {
      try {
        await this.testBackupIntegrity(backup);
        this.logger.log(`Backup ${backup.name} verified successfully`);
      } catch (error) {
        this.logger.error(`Backup ${backup.name} verification failed: ${error.message}`);
        await this.alertBackupFailure(backup, error);
      }
    }
  }

  private async testBackupIntegrity(backup: any) {
    // Download backup
    const data = await this.downloadBackup(backup.key);
    
    // Test if it's a valid gzip file
    if (!this.isValidGzip(data)) {
      throw new Error('Invalid backup format');
    }
    
    // Test database restore (in isolated environment)
    await this.testDatabaseRestore(data);
  }
}
```

### Backup Alerts

```typescript
// Backup alerting
@Injectable()
export class BackupAlertService {
  async alertBackupFailure(backup: any, error: Error) {
    const alert = {
      severity: 'HIGH',
      message: `Backup verification failed for ${backup.name}`,
      error: error.message,
      timestamp: new Date().toISOString(),
    };

    // Send to monitoring system
    await this.sendToSlack(alert);
    await this.sendToEmail(alert);
    await this.createIncident(alert);
  }

  private async sendToSlack(alert: any) {
    const webhook = process.env.SLACK_WEBHOOK_URL;
    
    await fetch(webhook, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        text: `🚨 ${alert.message}`,
        attachments: [{
          color: 'danger',
          fields: [
            { title: 'Error', value: alert.error, short: false },
            { title: 'Timestamp', value: alert.timestamp, short: true },
          ],
        }],
      }),
    });
  }
}
```

## Backup Automation

### Kubernetes CronJob

```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: database-backup
spec:
  schedule: "0 2 * * *" # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: backup
            image: postgres:13
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-secret
                  key: password
            command:
            - /bin/bash
            - -c
            - |
              DATE=$(date +%Y%m%d_%H%M%S)
              pg_dump -h postgres -U user myapp | gzip > /backup/backup_$DATE.sql.gz
              aws s3 cp /backup/backup_$DATE.sql.gz s3://backup-bucket/
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            emptyDir: {}
          restartPolicy: OnFailure
```

### Docker Compose Backup

```yaml
version: '3.8'
services:
  backup:
    image: alpine:latest
    volumes:
      - postgres_data:/var/lib/postgresql/data:ro
      - ./backups:/backups
    environment:
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
    command: >
      sh -c "
        apk add --no-cache postgresql-client aws-cli
        while true; do
          DATE=$$(date +%Y%m%d_%H%M%S)
          pg_dump -h postgres -U user myapp > /backups/backup_$$DATE.sql
          gzip /backups/backup_$$DATE.sql
          aws s3 cp /backups/backup_$$DATE.sql.gz s3://backup-bucket/
          sleep 86400
        done
      "
```

## Testing Recovery

### Recovery Testing

```typescript
// Recovery testing service
@Injectable()
export class RecoveryTestService {
  @Cron('0 0 * * 0') // Weekly on Sunday
  async testRecoveryProcedure() {
    const testEnvironment = await this.createTestEnvironment();
    
    try {
      // Test database recovery
      await this.testDatabaseRecovery(testEnvironment);
      
      // Test application recovery
      await this.testApplicationRecovery(testEnvironment);
      
      // Test data integrity
      await this.testDataIntegrity(testEnvironment);
      
      this.logger.log('Recovery test completed successfully');
    } catch (error) {
      this.logger.error(`Recovery test failed: ${error.message}`);
      await this.alertRecoveryTestFailure(error);
    } finally {
      await this.cleanupTestEnvironment(testEnvironment);
    }
  }
}
```

## Compliance and Retention

### Retention Policies

```typescript
// Backup retention service
@Injectable()
export class BackupRetentionService {
  private retentionPolicies = {
    daily: 30,   // Keep daily backups for 30 days
    weekly: 12,  // Keep weekly backups for 12 weeks
    monthly: 12, // Keep monthly backups for 12 months
    yearly: 7,   // Keep yearly backups for 7 years
  };

  @Cron('0 3 * * *') // Daily at 3 AM
  async enforceRetentionPolicy() {
    await this.cleanupDailyBackups();
    await this.cleanupWeeklyBackups();
    await this.cleanupMonthlyBackups();
    await this.cleanupYearlyBackups();
  }

  private async cleanupDailyBackups() {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.retentionPolicies.daily);
    
    const oldBackups = await this.findBackupsOlderThan(cutoffDate, 'daily');
    
    for (const backup of oldBackups) {
      await this.deleteBackup(backup);
      this.logger.log(`Deleted old daily backup: ${backup.name}`);
    }
  }
}
```

## Coming Soon

This documentation section is under development. Future additions will include:

- Advanced disaster recovery strategies
- Cross-region backup replication
- Backup encryption and security
- Compliance frameworks

## Next Steps

- [**Security Hardening**](./security) - Secure your backups
- [**Monitoring Setup**](./monitoring) - Monitor backup health
- [**Docker Deployment**](./docker) - Deploy with backup strategies
