---
sidebar_position: 3
---

# Security Hardening

Secure your Microgen-generated applications for production deployment.

## Overview

Security is paramount in production applications. This guide covers security best practices and hardening techniques for applications generated by Microgen.

## Application Security

### Authentication & Authorization

**JWT Security:**
```typescript
// Secure JWT configuration
@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { 
        expiresIn: '15m',
        algorithm: 'HS256'
      },
    }),
  ],
})
export class AuthModule {}
```

**Password Security:**
```typescript
import * as bcrypt from 'bcrypt';

const saltRounds = 12;
const hashedPassword = await bcrypt.hash(password, saltRounds);
```

### Input Validation

```typescript
import { IsEmail, IsString, MinLength } from 'class-validator';

export class CreateUserDto {
  @IsEmail()
  email: string;

  @IsString()
  @MinLength(8)
  password: string;
}
```

### Security Headers

```typescript
import helmet from 'helmet';

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

## Infrastructure Security

### Container Security

**Secure Dockerfile:**
```dockerfile
FROM node:18-alpine AS base

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Install dependencies
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copy application
COPY --chown=nextjs:nodejs . .

# Use non-root user
USER nextjs

# Security scanning
RUN npm audit --audit-level moderate

EXPOSE 3000
CMD ["npm", "start"]
```

### Network Security

**Docker Network Isolation:**
```yaml
version: '3.8'
services:
  app:
    networks:
      - frontend
      - backend
  
  database:
    networks:
      - backend
    # No external access

networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true
```

### Environment Security

**Secrets Management:**
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    environment:
      - DATABASE_URL_FILE=/run/secrets/db_url
    secrets:
      - db_url

secrets:
  db_url:
    file: ./secrets/database_url.txt
```

## Database Security

### Connection Security

```typescript
// Secure database configuration
const config: TypeOrmModuleOptions = {
  type: 'postgres',
  url: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? {
    rejectUnauthorized: false
  } : false,
  logging: process.env.NODE_ENV !== 'production',
};
```

### Query Security

```typescript
// Parameterized queries
const user = await this.userRepository
  .createQueryBuilder('user')
  .where('user.email = :email', { email })
  .getOne();
```

## API Security

### Rate Limiting

```typescript
import { ThrottlerModule } from '@nestjs/throttler';

@Module({
  imports: [
    ThrottlerModule.forRoot({
      ttl: 60,
      limit: 10,
    }),
  ],
})
export class AppModule {}
```

### CORS Configuration

```typescript
app.enableCors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
});
```

## Monitoring & Logging

### Security Logging

```typescript
// Security event logging
@Injectable()
export class SecurityLogger {
  private logger = new Logger(SecurityLogger.name);

  logFailedLogin(email: string, ip: string) {
    this.logger.warn(`Failed login attempt for ${email} from ${ip}`);
  }

  logSuspiciousActivity(userId: string, activity: string) {
    this.logger.error(`Suspicious activity: ${activity} by user ${userId}`);
  }
}
```

### Intrusion Detection

```typescript
// Simple intrusion detection
@Injectable()
export class IntrusionDetection {
  private attempts = new Map<string, number>();

  checkFailedAttempts(ip: string): boolean {
    const count = this.attempts.get(ip) || 0;
    if (count > 5) {
      throw new ForbiddenException('Too many failed attempts');
    }
    return true;
  }
}
```

## Vulnerability Management

### Dependency Scanning

```bash
# Regular security audits
npm audit
npm audit fix

# Automated scanning
npm install -g audit-ci
audit-ci --moderate
```

### Container Scanning

```bash
# Trivy scanning
trivy image myapp:latest

# Snyk scanning
snyk container test myapp:latest
```

## Compliance

### GDPR Compliance

```typescript
// Data anonymization
export class UserService {
  async anonymizeUser(userId: string) {
    await this.userRepository.update(userId, {
      email: `deleted-${userId}@example.com`,
      name: 'Deleted User',
      personalData: null,
    });
  }
}
```

### Audit Logging

```typescript
// Audit trail
@Injectable()
export class AuditLogger {
  async logDataAccess(userId: string, resource: string, action: string) {
    await this.auditRepository.save({
      userId,
      resource,
      action,
      timestamp: new Date(),
      ip: this.request.ip,
    });
  }
}
```

## Security Testing

### Automated Security Tests

```typescript
// Security test example
describe('Authentication Security', () => {
  test('should reject weak passwords', async () => {
    const weakPassword = '123';
    
    await expect(
      authService.register('<EMAIL>', weakPassword)
    ).rejects.toThrow('Password too weak');
  });

  test('should prevent SQL injection', async () => {
    const maliciousInput = "'; DROP TABLE users; --";
    
    await expect(
      userService.findByEmail(maliciousInput)
    ).not.toThrow();
  });
});
```

## Security Checklist

### Pre-deployment

- [ ] All dependencies updated
- [ ] Security audit passed
- [ ] Secrets properly managed
- [ ] HTTPS configured
- [ ] Security headers enabled
- [ ] Input validation implemented
- [ ] Rate limiting configured
- [ ] Logging configured

### Post-deployment

- [ ] Monitor security logs
- [ ] Regular security scans
- [ ] Incident response plan
- [ ] Backup and recovery tested
- [ ] Access controls reviewed

## Coming Soon

This documentation section is under development. Future additions will include:

- Advanced threat detection
- Security automation
- Compliance frameworks
- Incident response procedures

## Next Steps

- [**Monitoring Setup**](./monitoring) - Monitor your applications
- [**Scaling Strategies**](./scaling) - Scale securely
- [**Backup and Recovery**](./backup) - Protect your data
