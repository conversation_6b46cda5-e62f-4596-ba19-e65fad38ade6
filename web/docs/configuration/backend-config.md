---
sidebar_position: 2
---

# Backend Configuration

Configure your backend services with various frameworks, databases, and microservice communication patterns.

## Supported Frameworks

### NestJS (Recommended)
A progressive Node.js framework for building efficient and scalable server-side applications.

**Features:**
- TypeScript-first approach
- Decorator-based architecture
- Built-in dependency injection
- Native microservice support
- Swagger/OpenAPI integration
- Comprehensive testing utilities
- Built-in validation and serialization
- Guards, interceptors, and pipes
- WebSocket and GraphQL support

**Basic NestJS Setup:**
```bash
microgen create my-api --template api-only
# Select NestJS during interactive setup
```

**Microservice Configuration:**
```typescript
// app.module.ts
import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { UserModule } from './user/user.module';

@Module({
  imports: [
    ClientsModule.register([
      {
        name: 'USER_SERVICE',
        transport: Transport.KAFKA,
        options: {
          client: {
            clientId: 'user-service',
            brokers: ['localhost:9092'],
          },
          consumer: {
            groupId: 'user-consumer',
          },
        },
      },
      {
        name: 'NOTIFICATION_SERVICE',
        transport: Transport.GRPC,
        options: {
          package: 'notification',
          protoPath: join(__dirname, 'notification.proto'),
          url: 'localhost:50052',
        },
      },
    ]),
    UserModule,
  ],
})
export class AppModule {}
```

**Hybrid Application (HTTP + Microservice):**
```typescript
// main.ts
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { AppModule } from './app.module';

async function bootstrap() {
  // Create HTTP application
  const app = await NestFactory.create(AppModule);

  // Add Kafka microservice
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.KAFKA,
    options: {
      client: {
        clientId: 'user-service',
        brokers: ['localhost:9092'],
      },
      consumer: {
        groupId: 'user-consumer',
      },
    },
  });

  // Add gRPC microservice
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.GRPC,
    options: {
      package: 'user',
      protoPath: join(__dirname, 'user.proto'),
      url: '0.0.0.0:50051',
    },
  });

  // Start all microservices
  await app.startAllMicroservices();

  // Start HTTP server
  await app.listen(3000);
}
bootstrap();
```

### Express.js
Fast, unopinionated, minimalist web framework for Node.js.

**Features:**
- Lightweight and flexible
- Extensive middleware ecosystem
- Simple routing
- Quick prototyping

## Database Integration

### PostgreSQL (Recommended)
Advanced open-source relational database with excellent performance and features.

**Configuration:**
- TypeORM integration
- Migration support
- Connection pooling
- Environment-based configuration

### MySQL
Popular relational database management system.

**Features:**
- ACID compliance
- Replication support
- Performance optimization
- Wide hosting support

### MongoDB
Document-oriented NoSQL database for flexible data models.

**Features:**
- Schema flexibility
- Horizontal scaling
- Rich query language
- Aggregation framework

### SQLite
Lightweight, file-based SQL database perfect for development and small applications.

**Features:**
- Zero configuration
- Self-contained
- Cross-platform
- ACID transactions

## Microservice Communication

### Apache Kafka
Distributed event streaming platform for high-throughput data pipelines.

**NestJS Kafka Integration:**
```typescript
// kafka.module.ts
import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';

@Module({
  imports: [
    ClientsModule.register([
      {
        name: 'KAFKA_SERVICE',
        transport: Transport.KAFKA,
        options: {
          client: {
            clientId: 'my-app',
            brokers: ['localhost:9092'],
          },
          consumer: {
            groupId: 'my-consumer-group',
          },
        },
      },
    ]),
  ],
  exports: [ClientsModule],
})
export class KafkaModule {}
```

**Event Publishing:**
```typescript
// user.service.ts
import { Injectable, Inject } from '@nestjs/common';
import { ClientKafka } from '@nestjs/microservices';

@Injectable()
export class UserService {
  constructor(
    @Inject('KAFKA_SERVICE') private kafkaClient: ClientKafka,
  ) {}

  async onModuleInit() {
    await this.kafkaClient.connect();
  }

  async createUser(userData: CreateUserDto) {
    const user = await this.userRepository.save(userData);

    // Emit event to Kafka
    this.kafkaClient.emit('user.created', {
      userId: user.id,
      email: user.email,
      timestamp: new Date().toISOString(),
    });

    return user;
  }
}
```

**Event Consumption:**
```typescript
// notification.controller.ts
import { Controller } from '@nestjs/common';
import { EventPattern, Payload } from '@nestjs/microservices';

@Controller()
export class NotificationController {
  @EventPattern('user.created')
  async handleUserCreated(@Payload() data: any) {
    console.log('Sending welcome email to:', data.email);
    // Send welcome email logic
  }

  @EventPattern('order.completed')
  async handleOrderCompleted(@Payload() data: any) {
    console.log('Sending order confirmation:', data.orderId);
    // Send order confirmation logic
  }
}
```

**Use Cases:**
- Event sourcing and CQRS
- Real-time analytics and monitoring
- Log aggregation and processing
- Microservice coordination and orchestration
- Asynchronous task processing

### gRPC
High-performance, open-source RPC framework.

**Required Dependencies:**
```bash
npm install @nestjs/microservices @grpc/grpc-js @grpc/proto-loader
```

**NestJS gRPC Setup:**
```typescript
// grpc.module.ts
import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { join } from 'path';

@Module({
  imports: [
    ClientsModule.register([
      {
        name: 'USER_PACKAGE',
        transport: Transport.GRPC,
        options: {
          package: 'user',
          protoPath: join(__dirname, '../proto/user.proto'),
          url: 'localhost:50051',
        },
      },
    ]),
  ],
  exports: [ClientsModule],
})
export class GrpcModule {}
```

**gRPC Service Implementation:**
```typescript
// user-grpc.service.ts
import { Injectable, Inject, OnModuleInit } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { Observable } from 'rxjs';

interface UserGrpcService {
  findOne(data: { id: string }): Observable<any>;
  findMany(data: {}): Observable<any>;
}

@Injectable()
export class UserGrpcService implements OnModuleInit {
  private userService: UserGrpcService;

  constructor(@Inject('USER_PACKAGE') private client: ClientGrpc) {}

  onModuleInit() {
    this.userService = this.client.getService<UserGrpcService>('UserService');
  }

  findUser(id: string): Observable<any> {
    return this.userService.findOne({ id });
  }
}
```

**Features:**
- Protocol Buffers for efficient serialization
- Bi-directional streaming support
- Language agnostic contracts
- Built-in load balancing
- HTTP/2 based transport
- Strong typing with TypeScript

### Redis
In-memory data structure store used as database, cache, and message broker.

**Patterns:**
- Pub/Sub messaging
- Caching layer
- Session storage
- Rate limiting

### TCP
Direct TCP communication for high-performance scenarios.

**Benefits:**
- Low latency
- Custom protocols
- Full control
- Minimal overhead

### NATS
Cloud-native messaging system for microservices, IoT, and edge computing.

**Features:**
- Lightweight
- High performance
- Subject-based messaging
- Clustering support

### RabbitMQ
Robust message broker supporting multiple messaging protocols.

**Patterns:**
- Work queues
- Publish/Subscribe
- Routing
- RPC

## Authentication & Security

### JWT Authentication
JSON Web Token-based authentication system.

**Features:**
- Stateless authentication
- Role-based access control
- Token refresh mechanism
- Secure password hashing

### API Security
Built-in security features and best practices.

**Includes:**
- CORS configuration
- Rate limiting
- Input validation
- SQL injection prevention
- XSS protection

## API Documentation

### Swagger/OpenAPI
Automatic API documentation generation.

**Features:**
- Interactive API explorer
- Schema validation
- Code generation
- Testing interface

## Health Checks & Monitoring

### Health Endpoints
Built-in health check endpoints for monitoring.

**Endpoints:**
- `/health` - Basic health status
- `/health/detailed` - Comprehensive system status
- `/metrics` - Application metrics

### Logging
Structured logging with multiple levels and formats.

**Features:**
- JSON structured logs
- Log levels (error, warn, info, debug)
- Request/response logging
- Error tracking

## Environment Configuration

### Development
```env
NODE_ENV=development
PORT=3000
DATABASE_URL=postgresql://user:pass@localhost:5432/mydb
JWT_SECRET=your-secret-key
REDIS_URL=redis://localhost:6379
```

### Production
```env
NODE_ENV=production
PORT=8000
DATABASE_URL=***********************************/mydb
JWT_SECRET=secure-production-secret
REDIS_URL=redis://redis-cluster:6379
```

## Docker Configuration

### Multi-stage Dockerfile
Optimized Docker builds for production deployment.

```dockerfile
# Development stage
FROM node:18-alpine AS development
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
EXPOSE 3000
CMD ["npm", "run", "start:dev"]

# Production stage
FROM node:18-alpine AS production
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY --from=development /app/dist ./dist
EXPOSE 8000
CMD ["npm", "run", "start:prod"]
```

## Testing Configuration

### Unit Tests
Jest configuration for comprehensive testing.

**Features:**
- TypeScript support
- Mocking utilities
- Coverage reporting
- Watch mode

### Integration Tests
End-to-end testing setup.

**Includes:**
- Database testing
- API endpoint testing
- Authentication testing
- Error handling validation

## Performance Optimization

### Caching Strategies
Multiple caching layers for optimal performance.

**Types:**
- In-memory caching
- Redis caching
- Database query caching
- HTTP response caching

### Database Optimization
Query optimization and connection management.

**Features:**
- Connection pooling
- Query indexing
- Migration management
- Performance monitoring

## Next Steps

- [**Frontend Configuration**](./frontend-config) - Setup frontend applications
- [**Microservices Setup**](./microservices) - Inter-service communication
- [**Project Configuration**](./project-config) - Overall project setup
- [**Examples**](../examples/basic-api) - See backend configuration in action
