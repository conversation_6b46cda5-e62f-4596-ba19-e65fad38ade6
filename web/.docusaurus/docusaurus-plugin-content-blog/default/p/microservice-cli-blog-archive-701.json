{"archive": {"blogPosts": [{"id": "welcome", "metadata": {"permalink": "/microservice-cli/blog/welcome", "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/blog/2021-08-26-welcome/index.md", "source": "@site/blog/2021-08-26-welcome/index.md", "title": "Welcome", "description": "Docusaurus blogging features are powered by the blog plugin.", "date": "2021-08-26T00:00:00.000Z", "tags": [{"inline": false, "label": "Facebook", "permalink": "/microservice-cli/blog/tags/facebook", "description": "Facebook tag description"}, {"inline": false, "label": "Hello", "permalink": "/microservice-cli/blog/tags/hello", "description": "Hello tag description"}, {"inline": false, "label": "Docusaurus", "permalink": "/microservice-cli/blog/tags/docusaurus", "description": "Docusaurus tag description"}], "readingTime": 0.56, "hasTruncateMarker": true, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "page": {"permalink": "/microservice-cli/blog/authors/all-sebastien-lorber-articles"}, "socials": {"x": "https://x.com/sebastienlorber", "linkedin": "https://www.linkedin.com/in/sebastienlorber/", "github": "https://github.com/slorber", "newsletter": "https://thisweekinreact.com"}, "imageURL": "https://github.com/slorber.png", "key": "slorber"}, {"name": "<PERSON><PERSON><PERSON>", "title": "Ex-Meta Staff Engineer, Co-founder GreatFrontEnd", "url": "https://linkedin.com/in/yangshun", "page": {"permalink": "/microservice-cli/blog/authors/yangshun"}, "socials": {"x": "https://x.com/yangshunz", "linkedin": "https://www.linkedin.com/in/yangshun/", "github": "https://github.com/yangshun", "newsletter": "https://www.greatfrontend.com"}, "imageURL": "https://github.com/yangshun.png", "key": "yang<PERSON>n"}], "frontMatter": {"slug": "welcome", "title": "Welcome", "authors": ["slorber", "yang<PERSON>n"], "tags": ["facebook", "hello", "<PERSON>cusaurus"]}, "unlisted": false, "nextItem": {"title": "MDX Blog Post", "permalink": "/microservice-cli/blog/mdx-blog-post"}}, "content": "[Docusaurus blogging features](https://docusaurus.io/docs/blog) are powered by the [blog plugin](https://docusaurus.io/docs/api/plugins/@docusaurus/plugin-content-blog).\n\nHere are a few tips you might find useful.\n\n<!-- truncate -->\n\nSimply add Markdown files (or folders) to the `blog` directory.\n\nRegular blog authors can be added to `authors.yml`.\n\nThe blog post date can be extracted from filenames, such as:\n\n- `2019-05-30-welcome.md`\n- `2019-05-30-welcome/index.md`\n\nA blog post folder can be convenient to co-locate blog post images:\n\n![Docusaurus Plushie](./docusaurus-plushie-banner.jpeg)\n\nThe blog supports tags as well!\n\n**And if you don't want a blog**: just delete this directory, and use `blog: false` in your Docusaurus config."}, {"id": "mdx-blog-post", "metadata": {"permalink": "/microservice-cli/blog/mdx-blog-post", "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/blog/2021-08-01-mdx-blog-post.mdx", "source": "@site/blog/2021-08-01-mdx-blog-post.mdx", "title": "MDX Blog Post", "description": "Blog posts support Docusaurus Markdown features, such as MDX.", "date": "2021-08-01T00:00:00.000Z", "tags": [{"inline": false, "label": "Docusaurus", "permalink": "/microservice-cli/blog/tags/docusaurus", "description": "Docusaurus tag description"}], "readingTime": 0.27, "hasTruncateMarker": true, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "page": {"permalink": "/microservice-cli/blog/authors/all-sebastien-lorber-articles"}, "socials": {"x": "https://x.com/sebastienlorber", "linkedin": "https://www.linkedin.com/in/sebastienlorber/", "github": "https://github.com/slorber", "newsletter": "https://thisweekinreact.com"}, "imageURL": "https://github.com/slorber.png", "key": "slorber"}], "frontMatter": {"slug": "mdx-blog-post", "title": "MDX Blog Post", "authors": ["slorber"], "tags": ["<PERSON>cusaurus"]}, "unlisted": false, "prevItem": {"title": "Welcome", "permalink": "/microservice-cli/blog/welcome"}, "nextItem": {"title": "Long Blog Post", "permalink": "/microservice-cli/blog/long-blog-post"}}, "content": "Blog posts support [Docusaurus Markdown features](https://docusaurus.io/docs/markdown-features), such as [MDX](https://mdxjs.com/).\n\n:::tip\n\nUse the power of React to create interactive blog posts.\n\n:::\n\n{/* truncate */}\n\nFor example, use JSX to create an interactive button:\n\n```js\n<button onClick={() => alert('button clicked!')}>Click me!</button>\n```\n\n<button onClick={() => alert('button clicked!')}>Click me!</button>"}, {"id": "long-blog-post", "metadata": {"permalink": "/microservice-cli/blog/long-blog-post", "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/blog/2019-05-29-long-blog-post.md", "source": "@site/blog/2019-05-29-long-blog-post.md", "title": "Long Blog Post", "description": "This is the summary of a very long blog post,", "date": "2019-05-29T00:00:00.000Z", "tags": [{"inline": false, "label": "Hello", "permalink": "/microservice-cli/blog/tags/hello", "description": "Hello tag description"}, {"inline": false, "label": "Docusaurus", "permalink": "/microservice-cli/blog/tags/docusaurus", "description": "Docusaurus tag description"}], "readingTime": 2.04, "hasTruncateMarker": true, "authors": [{"name": "<PERSON><PERSON><PERSON>", "title": "Ex-Meta Staff Engineer, Co-founder GreatFrontEnd", "url": "https://linkedin.com/in/yangshun", "page": {"permalink": "/microservice-cli/blog/authors/yangshun"}, "socials": {"x": "https://x.com/yangshunz", "linkedin": "https://www.linkedin.com/in/yangshun/", "github": "https://github.com/yangshun", "newsletter": "https://www.greatfrontend.com"}, "imageURL": "https://github.com/yangshun.png", "key": "yang<PERSON>n"}], "frontMatter": {"slug": "long-blog-post", "title": "Long Blog Post", "authors": "yang<PERSON>n", "tags": ["hello", "<PERSON>cusaurus"]}, "unlisted": false, "prevItem": {"title": "MDX Blog Post", "permalink": "/microservice-cli/blog/mdx-blog-post"}, "nextItem": {"title": "First Blog Post", "permalink": "/microservice-cli/blog/first-blog-post"}}, "content": "This is the summary of a very long blog post,\n\nUse a `<!--` `truncate` `-->` comment to limit blog post size in the list view.\n\n<!-- truncate -->\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet"}, {"id": "first-blog-post", "metadata": {"permalink": "/microservice-cli/blog/first-blog-post", "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/blog/2019-05-28-first-blog-post.md", "source": "@site/blog/2019-05-28-first-blog-post.md", "title": "First Blog Post", "description": "Lorem ipsum dolor sit amet...", "date": "2019-05-28T00:00:00.000Z", "tags": [{"inline": false, "label": "<PERSON><PERSON>", "permalink": "/microservice-cli/blog/tags/hola", "description": "Hola tag description"}, {"inline": false, "label": "Docusaurus", "permalink": "/microservice-cli/blog/tags/docusaurus", "description": "Docusaurus tag description"}], "readingTime": 0.13, "hasTruncateMarker": true, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "page": {"permalink": "/microservice-cli/blog/authors/all-sebastien-lorber-articles"}, "socials": {"x": "https://x.com/sebastienlorber", "linkedin": "https://www.linkedin.com/in/sebastienlorber/", "github": "https://github.com/slorber", "newsletter": "https://thisweekinreact.com"}, "imageURL": "https://github.com/slorber.png", "key": "slorber"}, {"name": "<PERSON><PERSON><PERSON>", "title": "Ex-Meta Staff Engineer, Co-founder GreatFrontEnd", "url": "https://linkedin.com/in/yangshun", "page": {"permalink": "/microservice-cli/blog/authors/yangshun"}, "socials": {"x": "https://x.com/yangshunz", "linkedin": "https://www.linkedin.com/in/yangshun/", "github": "https://github.com/yangshun", "newsletter": "https://www.greatfrontend.com"}, "imageURL": "https://github.com/yangshun.png", "key": "yang<PERSON>n"}], "frontMatter": {"slug": "first-blog-post", "title": "First Blog Post", "authors": ["slorber", "yang<PERSON>n"], "tags": ["hola", "<PERSON>cusaurus"]}, "unlisted": false, "prevItem": {"title": "Long Blog Post", "permalink": "/microservice-cli/blog/long-blog-post"}}, "content": "Lorem ipsum dolor sit amet...\n\n<!-- truncate -->\n\n...consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet"}]}}