{"docusaurus-plugin-content-docs": {"default": {"path": "/microservice-cli/docs", "versions": [{"name": "current", "label": "Next", "isLast": true, "path": "/microservice-cli/docs", "mainDocId": "intro", "docs": [{"id": "commands/create", "path": "/microservice-cli/docs/commands/create", "sidebar": "tutorialSidebar"}, {"id": "commands/options", "path": "/microservice-cli/docs/commands/options", "sidebar": "tutorialSidebar"}, {"id": "contributing", "path": "/microservice-cli/docs/contributing", "sidebar": "tutorialSidebar"}, {"id": "examples/basic-api", "path": "/microservice-cli/docs/examples/basic-api", "sidebar": "tutorialSidebar"}, {"id": "installation", "path": "/microservice-cli/docs/installation", "sidebar": "tutorialSidebar"}, {"id": "intro", "path": "/microservice-cli/docs/intro", "sidebar": "tutorialSidebar"}, {"id": "quick-start", "path": "/microservice-cli/docs/quick-start", "sidebar": "tutorialSidebar"}, {"id": "templates/overview", "path": "/microservice-cli/docs/templates/overview", "sidebar": "tutorialSidebar"}], "draftIds": [], "sidebars": {"tutorialSidebar": {"link": {"path": "/microservice-cli/docs/intro", "label": "intro"}}}}], "breadcrumbs": true}}}