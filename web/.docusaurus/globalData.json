{"docusaurus-plugin-content-docs": {"default": {"path": "/microgen/docs", "versions": [{"name": "current", "label": "Next", "isLast": true, "path": "/microgen/docs", "mainDocId": "intro", "docs": [{"id": "advanced/best-practices", "path": "/microgen/docs/advanced/best-practices"}, {"id": "advanced/contributing-templates", "path": "/microgen/docs/advanced/contributing-templates"}, {"id": "advanced/custom-templates", "path": "/microgen/docs/advanced/custom-templates"}, {"id": "advanced/template-api", "path": "/microgen/docs/advanced/template-api"}, {"id": "advanced/template-examples", "path": "/microgen/docs/advanced/template-examples"}, {"id": "advanced/template-registry", "path": "/microgen/docs/advanced/template-registry"}, {"id": "advanced/testing", "path": "/microgen/docs/advanced/testing"}, {"id": "commands/create", "path": "/microgen/docs/commands/create", "sidebar": "tutorialSidebar"}, {"id": "commands/index", "path": "/microgen/docs/commands/"}, {"id": "commands/options", "path": "/microgen/docs/commands/options", "sidebar": "tutorialSidebar"}, {"id": "configuration/backend-config", "path": "/microgen/docs/configuration/backend-config"}, {"id": "configuration/frontend-config", "path": "/microgen/docs/configuration/frontend-config"}, {"id": "configuration/microservices", "path": "/microgen/docs/configuration/microservices"}, {"id": "configuration/project-config", "path": "/microgen/docs/configuration/project-config"}, {"id": "contributing", "path": "/microgen/docs/contributing", "sidebar": "tutorialSidebar"}, {"id": "deployment/backup", "path": "/microgen/docs/deployment/backup"}, {"id": "deployment/docker", "path": "/microgen/docs/deployment/docker"}, {"id": "deployment/monitoring", "path": "/microgen/docs/deployment/monitoring"}, {"id": "deployment/scaling", "path": "/microgen/docs/deployment/scaling"}, {"id": "deployment/security", "path": "/microgen/docs/deployment/security"}, {"id": "examples/basic-api", "path": "/microgen/docs/examples/basic-api", "sidebar": "tutorialSidebar"}, {"id": "examples/full-stack-app", "path": "/microgen/docs/examples/full-stack-app"}, {"id": "examples/microservice-architecture", "path": "/microgen/docs/examples/microservice-architecture"}, {"id": "installation", "path": "/microgen/docs/installation", "sidebar": "tutorialSidebar"}, {"id": "intro", "path": "/microgen/docs/intro", "sidebar": "tutorialSidebar"}, {"id": "quick-start", "path": "/microgen/docs/quick-start", "sidebar": "tutorialSidebar"}, {"id": "templates/overview", "path": "/microgen/docs/templates/overview", "sidebar": "tutorialSidebar"}], "draftIds": [], "sidebars": {"tutorialSidebar": {"link": {"path": "/microgen/docs/intro", "label": "intro"}}}}], "breadcrumbs": true}}}