{"docusaurus-plugin-content-docs": {"default": {"path": "/microgen/docs", "versions": [{"name": "current", "label": "Next", "isLast": true, "path": "/microgen/docs", "mainDocId": "intro", "docs": [{"id": "commands/create", "path": "/microgen/docs/commands/create", "sidebar": "tutorialSidebar"}, {"id": "commands/options", "path": "/microgen/docs/commands/options", "sidebar": "tutorialSidebar"}, {"id": "contributing", "path": "/microgen/docs/contributing", "sidebar": "tutorialSidebar"}, {"id": "examples/basic-api", "path": "/microgen/docs/examples/basic-api", "sidebar": "tutorialSidebar"}, {"id": "installation", "path": "/microgen/docs/installation", "sidebar": "tutorialSidebar"}, {"id": "intro", "path": "/microgen/docs/intro", "sidebar": "tutorialSidebar"}, {"id": "quick-start", "path": "/microgen/docs/quick-start", "sidebar": "tutorialSidebar"}, {"id": "templates/overview", "path": "/microgen/docs/templates/overview", "sidebar": "tutorialSidebar"}], "draftIds": [], "sidebars": {"tutorialSidebar": {"link": {"path": "/microgen/docs/intro", "label": "intro"}}}}], "breadcrumbs": true}}}