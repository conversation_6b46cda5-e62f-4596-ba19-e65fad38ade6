{"allContent": {"docusaurus-plugin-css-cascade-layers": {}, "docusaurus-plugin-content-docs": {"default": {"loadedVersions": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/microservice-cli/docs", "tagsPath": "/microservice-cli/docs/tags", "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/docs", "editUrlLocalized": "https://github.com/your-org/microservice-cli/tree/main/web/i18n/en/docusaurus-plugin-content-docs/current", "isLast": true, "routePriority": -1, "sidebarFilePath": "/home/<USER>/Documents/augment-projects/microservice-cli/web/sidebars.ts", "contentPath": "/home/<USER>/Documents/augment-projects/microservice-cli/web/docs", "contentPathLocalized": "/home/<USER>/Documents/augment-projects/microservice-cli/web/i18n/en/docusaurus-plugin-content-docs/current", "docs": [{"id": "commands/create", "title": "Create Command", "description": "The create command is the main command for generating new microservice projects. It supports both interactive and non-interactive modes with extensive customization options.", "source": "@site/docs/commands/create.md", "sourceDirName": "commands", "slug": "/commands/create", "permalink": "/microservice-cli/docs/commands/create", "draft": false, "unlisted": false, "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/docs/commands/create.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "Quick Start", "permalink": "/microservice-cli/docs/quick-start"}, "next": {"title": "Command Options", "permalink": "/microservice-cli/docs/commands/options"}}, {"id": "commands/options", "title": "Command Options", "description": "This page provides a comprehensive reference for all command-line options available in Microservice CLI.", "source": "@site/docs/commands/options.md", "sourceDirName": "commands", "slug": "/commands/options", "permalink": "/microservice-cli/docs/commands/options", "draft": false, "unlisted": false, "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/docs/commands/options.md", "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"sidebar_position": 2}, "sidebar": "tutorialSidebar", "previous": {"title": "Create Command", "permalink": "/microservice-cli/docs/commands/create"}, "next": {"title": "Template Overview", "permalink": "/microservice-cli/docs/templates/overview"}}, {"id": "contributing", "title": "Contributing", "description": "We welcome contributions to Microservice CLI! This guide will help you get started with contributing to the project.", "source": "@site/docs/contributing.md", "sourceDirName": ".", "slug": "/contributing", "permalink": "/microservice-cli/docs/contributing", "draft": false, "unlisted": false, "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/docs/contributing.md", "tags": [], "version": "current", "sidebarPosition": 10, "frontMatter": {"sidebar_position": 10}, "sidebar": "tutorialSidebar", "previous": {"title": "Basic API Example", "permalink": "/microservice-cli/docs/examples/basic-api"}}, {"id": "examples/basic-api", "title": "Basic API Example", "description": "This example demonstrates how to create a simple REST API using Microservice CLI with NestJS and PostgreSQL.", "source": "@site/docs/examples/basic-api.md", "sourceDirName": "examples", "slug": "/examples/basic-api", "permalink": "/microservice-cli/docs/examples/basic-api", "draft": false, "unlisted": false, "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/docs/examples/basic-api.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "Template Overview", "permalink": "/microservice-cli/docs/templates/overview"}, "next": {"title": "Contributing", "permalink": "/microservice-cli/docs/contributing"}}, {"id": "installation", "title": "Installation", "description": "Learn how to install and set up Microservice CLI on your development environment.", "source": "@site/docs/installation.md", "sourceDirName": ".", "slug": "/installation", "permalink": "/microservice-cli/docs/installation", "draft": false, "unlisted": false, "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/docs/installation.md", "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"sidebar_position": 2}, "sidebar": "tutorialSidebar", "previous": {"title": "Welcome to Microservice CLI", "permalink": "/microservice-cli/docs/intro"}, "next": {"title": "Quick Start", "permalink": "/microservice-cli/docs/quick-start"}}, {"id": "intro", "title": "Welcome to Microservice CLI", "description": "Microservice CLI is a powerful command-line tool designed to accelerate the development of modern microservice architectures. Generate complete projects with both frontend and backend components in seconds, not hours.", "source": "@site/docs/intro.md", "sourceDirName": ".", "slug": "/intro", "permalink": "/microservice-cli/docs/intro", "draft": false, "unlisted": false, "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/docs/intro.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "next": {"title": "Installation", "permalink": "/microservice-cli/docs/installation"}}, {"id": "quick-start", "title": "Quick Start", "description": "Get up and running with Microservice CLI in just 5 minutes! This guide will walk you through creating your first microservice project.", "source": "@site/docs/quick-start.md", "sourceDirName": ".", "slug": "/quick-start", "permalink": "/microservice-cli/docs/quick-start", "draft": false, "unlisted": false, "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/docs/quick-start.md", "tags": [], "version": "current", "sidebarPosition": 3, "frontMatter": {"sidebar_position": 3}, "sidebar": "tutorialSidebar", "previous": {"title": "Installation", "permalink": "/microservice-cli/docs/installation"}, "next": {"title": "Create Command", "permalink": "/microservice-cli/docs/commands/create"}}, {"id": "templates/overview", "title": "Template Overview", "description": "Microservice CLI provides three main project templates, each designed for different use cases and development scenarios. This guide explains each template and helps you choose the right one for your project.", "source": "@site/docs/templates/overview.md", "sourceDirName": "templates", "slug": "/templates/overview", "permalink": "/microservice-cli/docs/templates/overview", "draft": false, "unlisted": false, "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/docs/templates/overview.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "Command Options", "permalink": "/microservice-cli/docs/commands/options"}, "next": {"title": "Basic API Example", "permalink": "/microservice-cli/docs/examples/basic-api"}}], "drafts": [], "sidebars": {"tutorialSidebar": [{"type": "doc", "id": "intro"}, {"type": "doc", "id": "installation"}, {"type": "doc", "id": "quick-start"}, {"type": "category", "label": "CLI Commands", "items": [{"type": "doc", "id": "commands/create"}, {"type": "doc", "id": "commands/options"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Project Templates", "items": [{"type": "doc", "id": "templates/overview"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Examples", "items": [{"type": "doc", "id": "examples/basic-api"}], "collapsed": true, "collapsible": true}, {"type": "doc", "id": "contributing"}]}}]}}, "docusaurus-plugin-content-blog": {"default": {"blogSidebarTitle": "Recent posts", "blogPosts": [{"id": "welcome", "metadata": {"permalink": "/microservice-cli/blog/welcome", "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/blog/2021-08-26-welcome/index.md", "source": "@site/blog/2021-08-26-welcome/index.md", "title": "Welcome", "description": "Docusaurus blogging features are powered by the blog plugin.", "date": "2021-08-26T00:00:00.000Z", "tags": [{"inline": false, "label": "Facebook", "permalink": "/microservice-cli/blog/tags/facebook", "description": "Facebook tag description"}, {"inline": false, "label": "Hello", "permalink": "/microservice-cli/blog/tags/hello", "description": "Hello tag description"}, {"inline": false, "label": "Docusaurus", "permalink": "/microservice-cli/blog/tags/docusaurus", "description": "Docusaurus tag description"}], "readingTime": 0.56, "hasTruncateMarker": true, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "page": {"permalink": "/microservice-cli/blog/authors/all-sebastien-lorber-articles"}, "socials": {"x": "https://x.com/sebastienlorber", "linkedin": "https://www.linkedin.com/in/sebastienlorber/", "github": "https://github.com/slorber", "newsletter": "https://thisweekinreact.com"}, "imageURL": "https://github.com/slorber.png", "key": "slorber"}, {"name": "<PERSON><PERSON><PERSON>", "title": "Ex-Meta Staff Engineer, Co-founder GreatFrontEnd", "url": "https://linkedin.com/in/yangshun", "page": {"permalink": "/microservice-cli/blog/authors/yangshun"}, "socials": {"x": "https://x.com/yangshunz", "linkedin": "https://www.linkedin.com/in/yangshun/", "github": "https://github.com/yangshun", "newsletter": "https://www.greatfrontend.com"}, "imageURL": "https://github.com/yangshun.png", "key": "yang<PERSON>n"}], "frontMatter": {"slug": "welcome", "title": "Welcome", "authors": ["slorber", "yang<PERSON>n"], "tags": ["facebook", "hello", "<PERSON>cusaurus"]}, "unlisted": false, "nextItem": {"title": "MDX Blog Post", "permalink": "/microservice-cli/blog/mdx-blog-post"}}, "content": "[Docusaurus blogging features](https://docusaurus.io/docs/blog) are powered by the [blog plugin](https://docusaurus.io/docs/api/plugins/@docusaurus/plugin-content-blog).\n\nHere are a few tips you might find useful.\n\n<!-- truncate -->\n\nSimply add Markdown files (or folders) to the `blog` directory.\n\nRegular blog authors can be added to `authors.yml`.\n\nThe blog post date can be extracted from filenames, such as:\n\n- `2019-05-30-welcome.md`\n- `2019-05-30-welcome/index.md`\n\nA blog post folder can be convenient to co-locate blog post images:\n\n![Docusaurus Plushie](./docusaurus-plushie-banner.jpeg)\n\nThe blog supports tags as well!\n\n**And if you don't want a blog**: just delete this directory, and use `blog: false` in your Docusaurus config."}, {"id": "mdx-blog-post", "metadata": {"permalink": "/microservice-cli/blog/mdx-blog-post", "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/blog/2021-08-01-mdx-blog-post.mdx", "source": "@site/blog/2021-08-01-mdx-blog-post.mdx", "title": "MDX Blog Post", "description": "Blog posts support Docusaurus Markdown features, such as MDX.", "date": "2021-08-01T00:00:00.000Z", "tags": [{"inline": false, "label": "Docusaurus", "permalink": "/microservice-cli/blog/tags/docusaurus", "description": "Docusaurus tag description"}], "readingTime": 0.27, "hasTruncateMarker": true, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "page": {"permalink": "/microservice-cli/blog/authors/all-sebastien-lorber-articles"}, "socials": {"x": "https://x.com/sebastienlorber", "linkedin": "https://www.linkedin.com/in/sebastienlorber/", "github": "https://github.com/slorber", "newsletter": "https://thisweekinreact.com"}, "imageURL": "https://github.com/slorber.png", "key": "slorber"}], "frontMatter": {"slug": "mdx-blog-post", "title": "MDX Blog Post", "authors": ["slorber"], "tags": ["<PERSON>cusaurus"]}, "unlisted": false, "prevItem": {"title": "Welcome", "permalink": "/microservice-cli/blog/welcome"}, "nextItem": {"title": "Long Blog Post", "permalink": "/microservice-cli/blog/long-blog-post"}}, "content": "Blog posts support [Docusaurus Markdown features](https://docusaurus.io/docs/markdown-features), such as [MDX](https://mdxjs.com/).\n\n:::tip\n\nUse the power of React to create interactive blog posts.\n\n:::\n\n{/* truncate */}\n\nFor example, use JSX to create an interactive button:\n\n```js\n<button onClick={() => alert('button clicked!')}>Click me!</button>\n```\n\n<button onClick={() => alert('button clicked!')}>Click me!</button>"}, {"id": "long-blog-post", "metadata": {"permalink": "/microservice-cli/blog/long-blog-post", "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/blog/2019-05-29-long-blog-post.md", "source": "@site/blog/2019-05-29-long-blog-post.md", "title": "Long Blog Post", "description": "This is the summary of a very long blog post,", "date": "2019-05-29T00:00:00.000Z", "tags": [{"inline": false, "label": "Hello", "permalink": "/microservice-cli/blog/tags/hello", "description": "Hello tag description"}, {"inline": false, "label": "Docusaurus", "permalink": "/microservice-cli/blog/tags/docusaurus", "description": "Docusaurus tag description"}], "readingTime": 2.04, "hasTruncateMarker": true, "authors": [{"name": "<PERSON><PERSON><PERSON>", "title": "Ex-Meta Staff Engineer, Co-founder GreatFrontEnd", "url": "https://linkedin.com/in/yangshun", "page": {"permalink": "/microservice-cli/blog/authors/yangshun"}, "socials": {"x": "https://x.com/yangshunz", "linkedin": "https://www.linkedin.com/in/yangshun/", "github": "https://github.com/yangshun", "newsletter": "https://www.greatfrontend.com"}, "imageURL": "https://github.com/yangshun.png", "key": "yang<PERSON>n"}], "frontMatter": {"slug": "long-blog-post", "title": "Long Blog Post", "authors": "yang<PERSON>n", "tags": ["hello", "<PERSON>cusaurus"]}, "unlisted": false, "prevItem": {"title": "MDX Blog Post", "permalink": "/microservice-cli/blog/mdx-blog-post"}, "nextItem": {"title": "First Blog Post", "permalink": "/microservice-cli/blog/first-blog-post"}}, "content": "This is the summary of a very long blog post,\n\nUse a `<!--` `truncate` `-->` comment to limit blog post size in the list view.\n\n<!-- truncate -->\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet"}, {"id": "first-blog-post", "metadata": {"permalink": "/microservice-cli/blog/first-blog-post", "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/blog/2019-05-28-first-blog-post.md", "source": "@site/blog/2019-05-28-first-blog-post.md", "title": "First Blog Post", "description": "Lorem ipsum dolor sit amet...", "date": "2019-05-28T00:00:00.000Z", "tags": [{"inline": false, "label": "<PERSON><PERSON>", "permalink": "/microservice-cli/blog/tags/hola", "description": "Hola tag description"}, {"inline": false, "label": "Docusaurus", "permalink": "/microservice-cli/blog/tags/docusaurus", "description": "Docusaurus tag description"}], "readingTime": 0.13, "hasTruncateMarker": true, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "page": {"permalink": "/microservice-cli/blog/authors/all-sebastien-lorber-articles"}, "socials": {"x": "https://x.com/sebastienlorber", "linkedin": "https://www.linkedin.com/in/sebastienlorber/", "github": "https://github.com/slorber", "newsletter": "https://thisweekinreact.com"}, "imageURL": "https://github.com/slorber.png", "key": "slorber"}, {"name": "<PERSON><PERSON><PERSON>", "title": "Ex-Meta Staff Engineer, Co-founder GreatFrontEnd", "url": "https://linkedin.com/in/yangshun", "page": {"permalink": "/microservice-cli/blog/authors/yangshun"}, "socials": {"x": "https://x.com/yangshunz", "linkedin": "https://www.linkedin.com/in/yangshun/", "github": "https://github.com/yangshun", "newsletter": "https://www.greatfrontend.com"}, "imageURL": "https://github.com/yangshun.png", "key": "yang<PERSON>n"}], "frontMatter": {"slug": "first-blog-post", "title": "First Blog Post", "authors": ["slorber", "yang<PERSON>n"], "tags": ["hola", "<PERSON>cusaurus"]}, "unlisted": false, "prevItem": {"title": "Long Blog Post", "permalink": "/microservice-cli/blog/long-blog-post"}}, "content": "Lorem ipsum dolor sit amet...\n\n<!-- truncate -->\n\n...consectetur adipiscing elit. Pellentesque elementum dignissim ultricies. Fusce rhoncus ipsum tempor eros aliquam consequat. Lorem ipsum dolor sit amet"}], "blogListPaginated": [{"items": ["welcome", "mdx-blog-post", "long-blog-post", "first-blog-post"], "metadata": {"permalink": "/microservice-cli/blog", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 4, "blogDescription": "Blog", "blogTitle": "Blog"}}], "blogTags": {"/microservice-cli/blog/tags/facebook": {"inline": false, "label": "Facebook", "permalink": "/microservice-cli/blog/tags/facebook", "description": "Facebook tag description", "items": ["welcome"], "pages": [{"items": ["welcome"], "metadata": {"permalink": "/microservice-cli/blog/tags/facebook", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 1, "blogDescription": "Blog", "blogTitle": "Blog"}}], "unlisted": false}, "/microservice-cli/blog/tags/hello": {"inline": false, "label": "Hello", "permalink": "/microservice-cli/blog/tags/hello", "description": "Hello tag description", "items": ["welcome", "long-blog-post"], "pages": [{"items": ["welcome", "long-blog-post"], "metadata": {"permalink": "/microservice-cli/blog/tags/hello", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 2, "blogDescription": "Blog", "blogTitle": "Blog"}}], "unlisted": false}, "/microservice-cli/blog/tags/docusaurus": {"inline": false, "label": "Docusaurus", "permalink": "/microservice-cli/blog/tags/docusaurus", "description": "Docusaurus tag description", "items": ["welcome", "mdx-blog-post", "long-blog-post", "first-blog-post"], "pages": [{"items": ["welcome", "mdx-blog-post", "long-blog-post", "first-blog-post"], "metadata": {"permalink": "/microservice-cli/blog/tags/docusaurus", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 4, "blogDescription": "Blog", "blogTitle": "Blog"}}], "unlisted": false}, "/microservice-cli/blog/tags/hola": {"inline": false, "label": "<PERSON><PERSON>", "permalink": "/microservice-cli/blog/tags/hola", "description": "Hola tag description", "items": ["first-blog-post"], "pages": [{"items": ["first-blog-post"], "metadata": {"permalink": "/microservice-cli/blog/tags/hola", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 1, "blogDescription": "Blog", "blogTitle": "Blog"}}], "unlisted": false}}, "blogTagsListPath": "/microservice-cli/blog/tags", "authorsMap": {"yangshun": {"name": "<PERSON><PERSON><PERSON>", "title": "Ex-Meta Staff Engineer, Co-founder GreatFrontEnd", "url": "https://linkedin.com/in/yangshun", "page": {"permalink": "/microservice-cli/blog/authors/yangshun"}, "socials": {"x": "https://x.com/yangshunz", "linkedin": "https://www.linkedin.com/in/yangshun/", "github": "https://github.com/yangshun", "newsletter": "https://www.greatfrontend.com"}, "imageURL": "https://github.com/yangshun.png", "key": "yang<PERSON>n"}, "slorber": {"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Docusaurus maintainer", "url": "https://sebastienlorber.com", "page": {"permalink": "/microservice-cli/blog/authors/all-sebastien-lorber-articles"}, "socials": {"x": "https://x.com/sebastienlorber", "linkedin": "https://www.linkedin.com/in/sebastienlorber/", "github": "https://github.com/slorber", "newsletter": "https://thisweekinreact.com"}, "imageURL": "https://github.com/slorber.png", "key": "slorber"}}}}, "docusaurus-plugin-content-pages": {"default": [{"type": "jsx", "permalink": "/microservice-cli/", "source": "@site/src/pages/index.tsx"}, {"type": "mdx", "permalink": "/microservice-cli/markdown-page", "source": "@site/src/pages/markdown-page.md", "title": "Markdown page example", "description": "You don't need React to write simple standalone pages.", "frontMatter": {"title": "Markdown page example"}, "unlisted": false}]}, "docusaurus-plugin-debug": {}, "docusaurus-plugin-svgr": {}, "docusaurus-theme-classic": {}, "docusaurus-bootstrap-plugin": {}, "docusaurus-mdx-fallback-plugin": {}}}