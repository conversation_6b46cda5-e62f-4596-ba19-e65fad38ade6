import React from 'react';
import ComponentCreator from '@docusaurus/ComponentCreator';

export default [
  {
    path: '/microgen/__docusaurus/debug',
    component: ComponentCreator('/microgen/__docusaurus/debug', '9ab'),
    exact: true
  },
  {
    path: '/microgen/__docusaurus/debug/config',
    component: ComponentCreator('/microgen/__docusaurus/debug/config', 'd6f'),
    exact: true
  },
  {
    path: '/microgen/__docusaurus/debug/content',
    component: ComponentCreator('/microgen/__docusaurus/debug/content', '8da'),
    exact: true
  },
  {
    path: '/microgen/__docusaurus/debug/globalData',
    component: ComponentCreator('/microgen/__docusaurus/debug/globalData', '50c'),
    exact: true
  },
  {
    path: '/microgen/__docusaurus/debug/metadata',
    component: ComponentCreator('/microgen/__docusaurus/debug/metadata', 'b29'),
    exact: true
  },
  {
    path: '/microgen/__docusaurus/debug/registry',
    component: ComponentCreator('/microgen/__docusaurus/debug/registry', 'd8b'),
    exact: true
  },
  {
    path: '/microgen/__docusaurus/debug/routes',
    component: ComponentCreator('/microgen/__docusaurus/debug/routes', 'dfc'),
    exact: true
  },
  {
    path: '/microgen/blog',
    component: ComponentCreator('/microgen/blog', 'e38'),
    exact: true
  },
  {
    path: '/microgen/blog/archive',
    component: ComponentCreator('/microgen/blog/archive', '69d'),
    exact: true
  },
  {
    path: '/microgen/blog/authors',
    component: ComponentCreator('/microgen/blog/authors', '11b'),
    exact: true
  },
  {
    path: '/microgen/blog/authors/all-sebastien-lorber-articles',
    component: ComponentCreator('/microgen/blog/authors/all-sebastien-lorber-articles', '8b8'),
    exact: true
  },
  {
    path: '/microgen/blog/authors/yangshun',
    component: ComponentCreator('/microgen/blog/authors/yangshun', 'd6d'),
    exact: true
  },
  {
    path: '/microgen/blog/first-blog-post',
    component: ComponentCreator('/microgen/blog/first-blog-post', 'c26'),
    exact: true
  },
  {
    path: '/microgen/blog/long-blog-post',
    component: ComponentCreator('/microgen/blog/long-blog-post', '5d0'),
    exact: true
  },
  {
    path: '/microgen/blog/mdx-blog-post',
    component: ComponentCreator('/microgen/blog/mdx-blog-post', '87c'),
    exact: true
  },
  {
    path: '/microgen/blog/tags',
    component: ComponentCreator('/microgen/blog/tags', 'd02'),
    exact: true
  },
  {
    path: '/microgen/blog/tags/docusaurus',
    component: ComponentCreator('/microgen/blog/tags/docusaurus', '06d'),
    exact: true
  },
  {
    path: '/microgen/blog/tags/facebook',
    component: ComponentCreator('/microgen/blog/tags/facebook', '07a'),
    exact: true
  },
  {
    path: '/microgen/blog/tags/hello',
    component: ComponentCreator('/microgen/blog/tags/hello', '4cf'),
    exact: true
  },
  {
    path: '/microgen/blog/tags/hola',
    component: ComponentCreator('/microgen/blog/tags/hola', '175'),
    exact: true
  },
  {
    path: '/microgen/blog/welcome',
    component: ComponentCreator('/microgen/blog/welcome', 'fc8'),
    exact: true
  },
  {
    path: '/microgen/markdown-page',
    component: ComponentCreator('/microgen/markdown-page', 'd9b'),
    exact: true
  },
  {
    path: '/microgen/docs',
    component: ComponentCreator('/microgen/docs', 'f61'),
    routes: [
      {
        path: '/microgen/docs',
        component: ComponentCreator('/microgen/docs', '147'),
        routes: [
          {
            path: '/microgen/docs',
            component: ComponentCreator('/microgen/docs', '59b'),
            routes: [
              {
                path: '/microgen/docs/commands/create',
                component: ComponentCreator('/microgen/docs/commands/create', '2ad'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microgen/docs/commands/options',
                component: ComponentCreator('/microgen/docs/commands/options', '483'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microgen/docs/contributing',
                component: ComponentCreator('/microgen/docs/contributing', 'ac2'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microgen/docs/examples/basic-api',
                component: ComponentCreator('/microgen/docs/examples/basic-api', '80c'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microgen/docs/installation',
                component: ComponentCreator('/microgen/docs/installation', '683'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microgen/docs/intro',
                component: ComponentCreator('/microgen/docs/intro', '52b'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microgen/docs/quick-start',
                component: ComponentCreator('/microgen/docs/quick-start', '3a9'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microgen/docs/templates/overview',
                component: ComponentCreator('/microgen/docs/templates/overview', '8c1'),
                exact: true,
                sidebar: "tutorialSidebar"
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '/microgen/',
    component: ComponentCreator('/microgen/', '771'),
    exact: true
  },
  {
    path: '*',
    component: ComponentCreator('*'),
  },
];
