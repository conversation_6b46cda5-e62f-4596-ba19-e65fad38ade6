import React from 'react';
import ComponentCreator from '@docusaurus/ComponentCreator';

export default [
  {
    path: '/microgen/blog',
    component: ComponentCreator('/microgen/blog', 'e38'),
    exact: true
  },
  {
    path: '/microgen/blog/archive',
    component: ComponentCreator('/microgen/blog/archive', '69d'),
    exact: true
  },
  {
    path: '/microgen/blog/authors',
    component: ComponentCreator('/microgen/blog/authors', '11b'),
    exact: true
  },
  {
    path: '/microgen/blog/authors/all-sebastien-lorber-articles',
    component: ComponentCreator('/microgen/blog/authors/all-sebastien-lorber-articles', '8b8'),
    exact: true
  },
  {
    path: '/microgen/blog/authors/yangshun',
    component: ComponentCreator('/microgen/blog/authors/yangshun', 'd6d'),
    exact: true
  },
  {
    path: '/microgen/blog/first-blog-post',
    component: ComponentCreator('/microgen/blog/first-blog-post', 'c26'),
    exact: true
  },
  {
    path: '/microgen/blog/long-blog-post',
    component: ComponentCreator('/microgen/blog/long-blog-post', '5d0'),
    exact: true
  },
  {
    path: '/microgen/blog/mdx-blog-post',
    component: ComponentCreator('/microgen/blog/mdx-blog-post', '87c'),
    exact: true
  },
  {
    path: '/microgen/blog/tags',
    component: ComponentCreator('/microgen/blog/tags', 'd02'),
    exact: true
  },
  {
    path: '/microgen/blog/tags/docusaurus',
    component: ComponentCreator('/microgen/blog/tags/docusaurus', '06d'),
    exact: true
  },
  {
    path: '/microgen/blog/tags/facebook',
    component: ComponentCreator('/microgen/blog/tags/facebook', '07a'),
    exact: true
  },
  {
    path: '/microgen/blog/tags/hello',
    component: ComponentCreator('/microgen/blog/tags/hello', '4cf'),
    exact: true
  },
  {
    path: '/microgen/blog/tags/hola',
    component: ComponentCreator('/microgen/blog/tags/hola', '175'),
    exact: true
  },
  {
    path: '/microgen/blog/welcome',
    component: ComponentCreator('/microgen/blog/welcome', 'fc8'),
    exact: true
  },
  {
    path: '/microgen/markdown-page',
    component: ComponentCreator('/microgen/markdown-page', 'd9b'),
    exact: true
  },
  {
    path: '/microgen/docs',
    component: ComponentCreator('/microgen/docs', '9f2'),
    routes: [
      {
        path: '/microgen/docs',
        component: ComponentCreator('/microgen/docs', 'e8b'),
        routes: [
          {
            path: '/microgen/docs',
            component: ComponentCreator('/microgen/docs', 'd03'),
            routes: [
              {
                path: '/microgen/docs/advanced/best-practices',
                component: ComponentCreator('/microgen/docs/advanced/best-practices', 'c0b'),
                exact: true
              },
              {
                path: '/microgen/docs/advanced/contributing-templates',
                component: ComponentCreator('/microgen/docs/advanced/contributing-templates', 'ccc'),
                exact: true
              },
              {
                path: '/microgen/docs/advanced/custom-templates',
                component: ComponentCreator('/microgen/docs/advanced/custom-templates', '60c'),
                exact: true
              },
              {
                path: '/microgen/docs/advanced/template-api',
                component: ComponentCreator('/microgen/docs/advanced/template-api', 'c13'),
                exact: true
              },
              {
                path: '/microgen/docs/advanced/template-examples',
                component: ComponentCreator('/microgen/docs/advanced/template-examples', '9c7'),
                exact: true
              },
              {
                path: '/microgen/docs/advanced/template-registry',
                component: ComponentCreator('/microgen/docs/advanced/template-registry', '37a'),
                exact: true
              },
              {
                path: '/microgen/docs/advanced/testing',
                component: ComponentCreator('/microgen/docs/advanced/testing', '4e8'),
                exact: true
              },
              {
                path: '/microgen/docs/commands/',
                component: ComponentCreator('/microgen/docs/commands/', '935'),
                exact: true
              },
              {
                path: '/microgen/docs/commands/create',
                component: ComponentCreator('/microgen/docs/commands/create', '2ad'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microgen/docs/commands/options',
                component: ComponentCreator('/microgen/docs/commands/options', '483'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microgen/docs/configuration/backend-config',
                component: ComponentCreator('/microgen/docs/configuration/backend-config', 'de6'),
                exact: true
              },
              {
                path: '/microgen/docs/configuration/frontend-config',
                component: ComponentCreator('/microgen/docs/configuration/frontend-config', '0ff'),
                exact: true
              },
              {
                path: '/microgen/docs/configuration/microservices',
                component: ComponentCreator('/microgen/docs/configuration/microservices', '520'),
                exact: true
              },
              {
                path: '/microgen/docs/configuration/project-config',
                component: ComponentCreator('/microgen/docs/configuration/project-config', '2b3'),
                exact: true
              },
              {
                path: '/microgen/docs/contributing',
                component: ComponentCreator('/microgen/docs/contributing', 'ac2'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microgen/docs/deployment/backup',
                component: ComponentCreator('/microgen/docs/deployment/backup', '778'),
                exact: true
              },
              {
                path: '/microgen/docs/deployment/docker',
                component: ComponentCreator('/microgen/docs/deployment/docker', '02c'),
                exact: true
              },
              {
                path: '/microgen/docs/deployment/monitoring',
                component: ComponentCreator('/microgen/docs/deployment/monitoring', 'a16'),
                exact: true
              },
              {
                path: '/microgen/docs/deployment/scaling',
                component: ComponentCreator('/microgen/docs/deployment/scaling', 'f4a'),
                exact: true
              },
              {
                path: '/microgen/docs/deployment/security',
                component: ComponentCreator('/microgen/docs/deployment/security', '4a4'),
                exact: true
              },
              {
                path: '/microgen/docs/examples/basic-api',
                component: ComponentCreator('/microgen/docs/examples/basic-api', '80c'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microgen/docs/examples/full-stack-app',
                component: ComponentCreator('/microgen/docs/examples/full-stack-app', '2b1'),
                exact: true
              },
              {
                path: '/microgen/docs/examples/microservice-architecture',
                component: ComponentCreator('/microgen/docs/examples/microservice-architecture', '339'),
                exact: true
              },
              {
                path: '/microgen/docs/installation',
                component: ComponentCreator('/microgen/docs/installation', '683'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microgen/docs/intro',
                component: ComponentCreator('/microgen/docs/intro', '52b'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microgen/docs/quick-start',
                component: ComponentCreator('/microgen/docs/quick-start', '3a9'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microgen/docs/templates/overview',
                component: ComponentCreator('/microgen/docs/templates/overview', '8c1'),
                exact: true,
                sidebar: "tutorialSidebar"
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '/microgen/',
    component: ComponentCreator('/microgen/', '771'),
    exact: true
  },
  {
    path: '*',
    component: ComponentCreator('*'),
  },
];
