import React from 'react';
import ComponentCreator from '@docusaurus/ComponentCreator';

export default [
  {
    path: '/microservice-cli/__docusaurus/debug',
    component: ComponentCreator('/microservice-cli/__docusaurus/debug', '3b2'),
    exact: true
  },
  {
    path: '/microservice-cli/__docusaurus/debug/config',
    component: ComponentCreator('/microservice-cli/__docusaurus/debug/config', 'b76'),
    exact: true
  },
  {
    path: '/microservice-cli/__docusaurus/debug/content',
    component: ComponentCreator('/microservice-cli/__docusaurus/debug/content', 'dd2'),
    exact: true
  },
  {
    path: '/microservice-cli/__docusaurus/debug/globalData',
    component: ComponentCreator('/microservice-cli/__docusaurus/debug/globalData', '340'),
    exact: true
  },
  {
    path: '/microservice-cli/__docusaurus/debug/metadata',
    component: ComponentCreator('/microservice-cli/__docusaurus/debug/metadata', '4d3'),
    exact: true
  },
  {
    path: '/microservice-cli/__docusaurus/debug/registry',
    component: ComponentCreator('/microservice-cli/__docusaurus/debug/registry', '18a'),
    exact: true
  },
  {
    path: '/microservice-cli/__docusaurus/debug/routes',
    component: ComponentCreator('/microservice-cli/__docusaurus/debug/routes', '20f'),
    exact: true
  },
  {
    path: '/microservice-cli/blog',
    component: ComponentCreator('/microservice-cli/blog', 'df6'),
    exact: true
  },
  {
    path: '/microservice-cli/blog/archive',
    component: ComponentCreator('/microservice-cli/blog/archive', 'f3e'),
    exact: true
  },
  {
    path: '/microservice-cli/blog/authors',
    component: ComponentCreator('/microservice-cli/blog/authors', '41e'),
    exact: true
  },
  {
    path: '/microservice-cli/blog/authors/all-sebastien-lorber-articles',
    component: ComponentCreator('/microservice-cli/blog/authors/all-sebastien-lorber-articles', '788'),
    exact: true
  },
  {
    path: '/microservice-cli/blog/authors/yangshun',
    component: ComponentCreator('/microservice-cli/blog/authors/yangshun', '96a'),
    exact: true
  },
  {
    path: '/microservice-cli/blog/first-blog-post',
    component: ComponentCreator('/microservice-cli/blog/first-blog-post', 'f0b'),
    exact: true
  },
  {
    path: '/microservice-cli/blog/long-blog-post',
    component: ComponentCreator('/microservice-cli/blog/long-blog-post', '798'),
    exact: true
  },
  {
    path: '/microservice-cli/blog/mdx-blog-post',
    component: ComponentCreator('/microservice-cli/blog/mdx-blog-post', '6dd'),
    exact: true
  },
  {
    path: '/microservice-cli/blog/tags',
    component: ComponentCreator('/microservice-cli/blog/tags', 'ffb'),
    exact: true
  },
  {
    path: '/microservice-cli/blog/tags/docusaurus',
    component: ComponentCreator('/microservice-cli/blog/tags/docusaurus', '537'),
    exact: true
  },
  {
    path: '/microservice-cli/blog/tags/facebook',
    component: ComponentCreator('/microservice-cli/blog/tags/facebook', '555'),
    exact: true
  },
  {
    path: '/microservice-cli/blog/tags/hello',
    component: ComponentCreator('/microservice-cli/blog/tags/hello', '2a2'),
    exact: true
  },
  {
    path: '/microservice-cli/blog/tags/hola',
    component: ComponentCreator('/microservice-cli/blog/tags/hola', '99a'),
    exact: true
  },
  {
    path: '/microservice-cli/blog/welcome',
    component: ComponentCreator('/microservice-cli/blog/welcome', '4fe'),
    exact: true
  },
  {
    path: '/microservice-cli/markdown-page',
    component: ComponentCreator('/microservice-cli/markdown-page', 'c50'),
    exact: true
  },
  {
    path: '/microservice-cli/docs',
    component: ComponentCreator('/microservice-cli/docs', '9f2'),
    routes: [
      {
        path: '/microservice-cli/docs',
        component: ComponentCreator('/microservice-cli/docs', '5bf'),
        routes: [
          {
            path: '/microservice-cli/docs',
            component: ComponentCreator('/microservice-cli/docs', '041'),
            routes: [
              {
                path: '/microservice-cli/docs/commands/create',
                component: ComponentCreator('/microservice-cli/docs/commands/create', 'dd1'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microservice-cli/docs/commands/options',
                component: ComponentCreator('/microservice-cli/docs/commands/options', '9ff'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microservice-cli/docs/contributing',
                component: ComponentCreator('/microservice-cli/docs/contributing', '05a'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microservice-cli/docs/examples/basic-api',
                component: ComponentCreator('/microservice-cli/docs/examples/basic-api', '8d6'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microservice-cli/docs/installation',
                component: ComponentCreator('/microservice-cli/docs/installation', '13d'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microservice-cli/docs/intro',
                component: ComponentCreator('/microservice-cli/docs/intro', 'af4'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microservice-cli/docs/quick-start',
                component: ComponentCreator('/microservice-cli/docs/quick-start', '53b'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/microservice-cli/docs/templates/overview',
                component: ComponentCreator('/microservice-cli/docs/templates/overview', 'adb'),
                exact: true,
                sidebar: "tutorialSidebar"
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '/microservice-cli/',
    component: ComponentCreator('/microservice-cli/', '3bd'),
    exact: true
  },
  {
    path: '*',
    component: ComponentCreator('*'),
  },
];
