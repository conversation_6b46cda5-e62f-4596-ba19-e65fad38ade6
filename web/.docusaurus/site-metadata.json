{"docusaurusVersion": "3.8.1", "siteVersion": "1.0.0", "pluginVersions": {"docusaurus-plugin-css-cascade-layers": {"type": "package", "name": "@docusaurus/plugin-css-cascade-layers", "version": "3.8.1"}, "docusaurus-plugin-content-docs": {"type": "package", "name": "@docusaurus/plugin-content-docs", "version": "3.8.1"}, "docusaurus-plugin-content-blog": {"type": "package", "name": "@docusaurus/plugin-content-blog", "version": "3.8.1"}, "docusaurus-plugin-content-pages": {"type": "package", "name": "@docusaurus/plugin-content-pages", "version": "3.8.1"}, "docusaurus-plugin-sitemap": {"type": "package", "name": "@docusaurus/plugin-sitemap", "version": "3.8.1"}, "docusaurus-plugin-svgr": {"type": "package", "name": "@docusaurus/plugin-svgr", "version": "3.8.1"}, "docusaurus-theme-classic": {"type": "package", "name": "@docusaurus/theme-classic", "version": "3.8.1"}}}