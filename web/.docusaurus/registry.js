export default {
  "01a85c17": [() => import(/* webpackChunkName: "01a85c17" */ "@theme/BlogTagsListPage"), "@theme/BlogTagsListPage", require.resolveWeak("@theme/BlogTagsListPage")],
  "01feaeb7": [() => import(/* webpackChunkName: "01feaeb7" */ "@site/docs/advanced/testing.md"), "@site/docs/advanced/testing.md", require.resolveWeak("@site/docs/advanced/testing.md")],
  "0e384e19": [() => import(/* webpackChunkName: "0e384e19" */ "@site/docs/intro.md"), "@site/docs/intro.md", require.resolveWeak("@site/docs/intro.md")],
  "12f2b782": [() => import(/* webpackChunkName: "12f2b782" */ "@site/docs/templates/overview.md"), "@site/docs/templates/overview.md", require.resolveWeak("@site/docs/templates/overview.md")],
  "14b5dd18": [() => import(/* webpackChunkName: "14b5dd18" */ "@site/docs/commands/options.md"), "@site/docs/commands/options.md", require.resolveWeak("@site/docs/commands/options.md")],
  "17896441": [() => import(/* webpackChunkName: "17896441" */ "@theme/DocItem"), "@theme/DocItem", require.resolveWeak("@theme/DocItem")],
  "1df93b7f": [() => import(/* webpackChunkName: "1df93b7f" */ "@site/src/pages/index.tsx"), "@site/src/pages/index.tsx", require.resolveWeak("@site/src/pages/index.tsx")],
  "1f391b9e": [() => import(/* webpackChunkName: "1f391b9e" */ "@theme/MDXPage"), "@theme/MDXPage", require.resolveWeak("@theme/MDXPage")],
  "256f8711": [() => import(/* webpackChunkName: "256f8711" */ "@site/docs/examples/microservice-architecture.md"), "@site/docs/examples/microservice-architecture.md", require.resolveWeak("@site/docs/examples/microservice-architecture.md")],
  "29111719": [() => import(/* webpackChunkName: "29111719" */ "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-authors-yangshun-143.json"), "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-authors-yangshun-143.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-authors-yangshun-143.json")],
  "2fd99a78": [() => import(/* webpackChunkName: "2fd99a78" */ "@site/docs/deployment/monitoring.md"), "@site/docs/deployment/monitoring.md", require.resolveWeak("@site/docs/deployment/monitoring.md")],
  "33fc5bb8": [() => import(/* webpackChunkName: "33fc5bb8" */ "@theme/Blog/Pages/BlogAuthorsPostsPage"), "@theme/Blog/Pages/BlogAuthorsPostsPage", require.resolveWeak("@theme/Blog/Pages/BlogAuthorsPostsPage")],
  "36994c47": [() => import(/* webpackChunkName: "36994c47" */ "@generated/docusaurus-plugin-content-blog/default/__plugin.json"), "@generated/docusaurus-plugin-content-blog/default/__plugin.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/__plugin.json")],
  "38d9793d": [() => import(/* webpackChunkName: "38d9793d" */ "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-01d.json"), "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-01d.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-01d.json")],
  "393be207": [() => import(/* webpackChunkName: "393be207" */ "@site/src/pages/markdown-page.md"), "@site/src/pages/markdown-page.md", require.resolveWeak("@site/src/pages/markdown-page.md")],
  "396900e1": [() => import(/* webpackChunkName: "396900e1" */ "@site/docs/advanced/template-examples.md"), "@site/docs/advanced/template-examples.md", require.resolveWeak("@site/docs/advanced/template-examples.md")],
  "3b8c55ea": [() => import(/* webpackChunkName: "3b8c55ea" */ "@site/docs/installation.md"), "@site/docs/installation.md", require.resolveWeak("@site/docs/installation.md")],
  "3d32b9bc": [() => import(/* webpackChunkName: "3d32b9bc" */ "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-tags-7f2.json"), "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-tags-7f2.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-tags-7f2.json")],
  "475fc1c4": [() => import(/* webpackChunkName: "475fc1c4" */ "@site/docs/commands/create.md"), "@site/docs/commands/create.md", require.resolveWeak("@site/docs/commands/create.md")],
  "4d54d076": [() => import(/* webpackChunkName: "4d54d076" */ "@site/docs/contributing.md"), "@site/docs/contributing.md", require.resolveWeak("@site/docs/contributing.md")],
  "59362658": [() => import(/* webpackChunkName: "59362658" */ "@site/blog/2021-08-01-mdx-blog-post.mdx"), "@site/blog/2021-08-01-mdx-blog-post.mdx", require.resolveWeak("@site/blog/2021-08-01-mdx-blog-post.mdx")],
  "5e95c892": [() => import(/* webpackChunkName: "5e95c892" */ "@theme/DocsRoot"), "@theme/DocsRoot", require.resolveWeak("@theme/DocsRoot")],
  "5e9f5e1a": [() => import(/* webpackChunkName: "5e9f5e1a" */ "@generated/docusaurus.config"), "@generated/docusaurus.config", require.resolveWeak("@generated/docusaurus.config")],
  "6108138b": [() => import(/* webpackChunkName: "6108138b" */ "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-archive-b40.json"), "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-archive-b40.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-archive-b40.json")],
  "621db11d": [() => import(/* webpackChunkName: "621db11d" */ "@theme/Blog/Pages/BlogAuthorsListPage"), "@theme/Blog/Pages/BlogAuthorsListPage", require.resolveWeak("@theme/Blog/Pages/BlogAuthorsListPage")],
  "649a3c78": [() => import(/* webpackChunkName: "649a3c78" */ "@site/docs/examples/basic-api.md"), "@site/docs/examples/basic-api.md", require.resolveWeak("@site/docs/examples/basic-api.md")],
  "65a80511": [() => import(/* webpackChunkName: "65a80511" */ "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-tags-hola-e4b.json"), "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-tags-hola-e4b.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-tags-hola-e4b.json")],
  "6875c492": [() => import(/* webpackChunkName: "6875c492" */ "@theme/BlogTagsPostsPage"), "@theme/BlogTagsPostsPage", require.resolveWeak("@theme/BlogTagsPostsPage")],
  "6911e679": [() => import(/* webpackChunkName: "6911e679" */ "@site/docs/deployment/security.md"), "@site/docs/deployment/security.md", require.resolveWeak("@site/docs/deployment/security.md")],
  "72e14192": [() => import(/* webpackChunkName: "72e14192" */ "@site/docs/quick-start.md"), "@site/docs/quick-start.md", require.resolveWeak("@site/docs/quick-start.md")],
  "73664a40": [() => import(/* webpackChunkName: "73664a40" */ "@site/blog/2019-05-29-long-blog-post.md"), "@site/blog/2019-05-29-long-blog-post.md", require.resolveWeak("@site/blog/2019-05-29-long-blog-post.md")],
  "7497d71c": [() => import(/* webpackChunkName: "7497d71c" */ "@site/docs/advanced/template-registry.md"), "@site/docs/advanced/template-registry.md", require.resolveWeak("@site/docs/advanced/template-registry.md")],
  "7661071f": [() => import(/* webpackChunkName: "7661071f" */ "@site/blog/2021-08-26-welcome/index.md?truncated=true"), "@site/blog/2021-08-26-welcome/index.md?truncated=true", require.resolveWeak("@site/blog/2021-08-26-welcome/index.md?truncated=true")],
  "78a6fab6": [() => import(/* webpackChunkName: "78a6fab6" */ "@site/docs/configuration/microservices.md"), "@site/docs/configuration/microservices.md", require.resolveWeak("@site/docs/configuration/microservices.md")],
  "7984dccc": [() => import(/* webpackChunkName: "7984dccc" */ "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-authors-all-sebastien-lorber-articles-fb0.json"), "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-authors-all-sebastien-lorber-articles-fb0.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-authors-all-sebastien-lorber-articles-fb0.json")],
  "7cc2e5fd": [() => import(/* webpackChunkName: "7cc2e5fd" */ "@site/docs/advanced/custom-templates.md"), "@site/docs/advanced/custom-templates.md", require.resolveWeak("@site/docs/advanced/custom-templates.md")],
  "814f3328": [() => import(/* webpackChunkName: "814f3328" */ "~blog/default/blog-post-list-prop-default.json"), "~blog/default/blog-post-list-prop-default.json", require.resolveWeak("~blog/default/blog-post-list-prop-default.json")],
  "85d5f84e": [() => import(/* webpackChunkName: "85d5f84e" */ "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-tags-facebook-c94.json"), "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-tags-facebook-c94.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-tags-facebook-c94.json")],
  "85e7a149": [() => import(/* webpackChunkName: "85e7a149" */ "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-authors-690.json"), "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-authors-690.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-authors-690.json")],
  "8717b14a": [() => import(/* webpackChunkName: "8717b14a" */ "@site/blog/2019-05-29-long-blog-post.md?truncated=true"), "@site/blog/2019-05-29-long-blog-post.md?truncated=true", require.resolveWeak("@site/blog/2019-05-29-long-blog-post.md?truncated=true")],
  "88758378": [() => import(/* webpackChunkName: "88758378" */ "@site/docs/configuration/frontend-config.md"), "@site/docs/configuration/frontend-config.md", require.resolveWeak("@site/docs/configuration/frontend-config.md")],
  "925b3f96": [() => import(/* webpackChunkName: "925b3f96" */ "@site/blog/2019-05-28-first-blog-post.md?truncated=true"), "@site/blog/2019-05-28-first-blog-post.md?truncated=true", require.resolveWeak("@site/blog/2019-05-28-first-blog-post.md?truncated=true")],
  "93934ef1": [() => import(/* webpackChunkName: "93934ef1" */ "@site/docs/advanced/contributing-templates.md"), "@site/docs/advanced/contributing-templates.md", require.resolveWeak("@site/docs/advanced/contributing-templates.md")],
  "9b577d45": [() => import(/* webpackChunkName: "9b577d45" */ "@site/docs/commands/index.md"), "@site/docs/commands/index.md", require.resolveWeak("@site/docs/commands/index.md")],
  "9d68eb7d": [() => import(/* webpackChunkName: "9d68eb7d" */ "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-tags-hello-047.json"), "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-tags-hello-047.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-tags-hello-047.json")],
  "9e4087bc": [() => import(/* webpackChunkName: "9e4087bc" */ "@theme/BlogArchivePage"), "@theme/BlogArchivePage", require.resolveWeak("@theme/BlogArchivePage")],
  "a6aa9e1f": [() => import(/* webpackChunkName: "a6aa9e1f" */ "@theme/BlogListPage"), "@theme/BlogListPage", require.resolveWeak("@theme/BlogListPage")],
  "a7456010": [() => import(/* webpackChunkName: "a7456010" */ "@generated/docusaurus-plugin-content-pages/default/__plugin.json"), "@generated/docusaurus-plugin-content-pages/default/__plugin.json", require.resolveWeak("@generated/docusaurus-plugin-content-pages/default/__plugin.json")],
  "a7bd4aaa": [() => import(/* webpackChunkName: "a7bd4aaa" */ "@theme/DocVersionRoot"), "@theme/DocVersionRoot", require.resolveWeak("@theme/DocVersionRoot")],
  "a94703ab": [() => import(/* webpackChunkName: "a94703ab" */ "@theme/DocRoot"), "@theme/DocRoot", require.resolveWeak("@theme/DocRoot")],
  "aba21aa0": [() => import(/* webpackChunkName: "aba21aa0" */ "@generated/docusaurus-plugin-content-docs/default/__plugin.json"), "@generated/docusaurus-plugin-content-docs/default/__plugin.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/__plugin.json")],
  "acecf23e": [() => import(/* webpackChunkName: "acecf23e" */ "~blog/default/blogMetadata-default.json"), "~blog/default/blogMetadata-default.json", require.resolveWeak("~blog/default/blogMetadata-default.json")],
  "ad95b843": [() => import(/* webpackChunkName: "ad95b843" */ "@generated/docusaurus-plugin-content-docs/default/p/microgen-docs-90f.json"), "@generated/docusaurus-plugin-content-docs/default/p/microgen-docs-90f.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/microgen-docs-90f.json")],
  "b723a7f0": [() => import(/* webpackChunkName: "b723a7f0" */ "@site/docs/advanced/template-api.md"), "@site/docs/advanced/template-api.md", require.resolveWeak("@site/docs/advanced/template-api.md")],
  "bdbc3a07": [() => import(/* webpackChunkName: "bdbc3a07" */ "@site/docs/deployment/scaling.md"), "@site/docs/deployment/scaling.md", require.resolveWeak("@site/docs/deployment/scaling.md")],
  "ccc49370": [() => import(/* webpackChunkName: "ccc49370" */ "@theme/BlogPostPage"), "@theme/BlogPostPage", require.resolveWeak("@theme/BlogPostPage")],
  "d4228973": [() => import(/* webpackChunkName: "d4228973" */ "@site/docs/deployment/backup.md"), "@site/docs/deployment/backup.md", require.resolveWeak("@site/docs/deployment/backup.md")],
  "d9f32620": [() => import(/* webpackChunkName: "d9f32620" */ "@site/blog/2021-08-26-welcome/index.md"), "@site/blog/2021-08-26-welcome/index.md", require.resolveWeak("@site/blog/2021-08-26-welcome/index.md")],
  "da31339f": [() => import(/* webpackChunkName: "da31339f" */ "@site/docs/examples/full-stack-app.md"), "@site/docs/examples/full-stack-app.md", require.resolveWeak("@site/docs/examples/full-stack-app.md")],
  "db8beaa3": [() => import(/* webpackChunkName: "db8beaa3" */ "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-tags-docusaurus-193.json"), "@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-tags-docusaurus-193.json", require.resolveWeak("@generated/docusaurus-plugin-content-blog/default/p/microgen-blog-tags-docusaurus-193.json")],
  "dbe3b6b9": [() => import(/* webpackChunkName: "dbe3b6b9" */ "@site/docs/advanced/best-practices.md"), "@site/docs/advanced/best-practices.md", require.resolveWeak("@site/docs/advanced/best-practices.md")],
  "e273c56f": [() => import(/* webpackChunkName: "e273c56f" */ "@site/blog/2019-05-28-first-blog-post.md"), "@site/blog/2019-05-28-first-blog-post.md", require.resolveWeak("@site/blog/2019-05-28-first-blog-post.md")],
  "e46aea49": [() => import(/* webpackChunkName: "e46aea49" */ "@site/docs/configuration/backend-config.md"), "@site/docs/configuration/backend-config.md", require.resolveWeak("@site/docs/configuration/backend-config.md")],
  "ede4de23": [() => import(/* webpackChunkName: "ede4de23" */ "@site/docs/configuration/project-config.md"), "@site/docs/configuration/project-config.md", require.resolveWeak("@site/docs/configuration/project-config.md")],
  "f4f34a3a": [() => import(/* webpackChunkName: "f4f34a3a" */ "@site/blog/2021-08-01-mdx-blog-post.mdx?truncated=true"), "@site/blog/2021-08-01-mdx-blog-post.mdx?truncated=true", require.resolveWeak("@site/blog/2021-08-01-mdx-blog-post.mdx?truncated=true")],
  "ff8b1e34": [() => import(/* webpackChunkName: "ff8b1e34" */ "@site/docs/deployment/docker.md"), "@site/docs/deployment/docker.md", require.resolveWeak("@site/docs/deployment/docker.md")],};
