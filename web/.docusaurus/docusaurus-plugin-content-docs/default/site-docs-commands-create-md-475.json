{"id": "commands/create", "title": "Create Command", "description": "The create command is the main command for generating new microservice projects. It supports both interactive and non-interactive modes with extensive customization options.", "source": "@site/docs/commands/create.md", "sourceDirName": "commands", "slug": "/commands/create", "permalink": "/microservice-cli/docs/commands/create", "draft": false, "unlisted": false, "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/docs/commands/create.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "Quick Start", "permalink": "/microservice-cli/docs/quick-start"}, "next": {"title": "Command Options", "permalink": "/microservice-cli/docs/commands/options"}}