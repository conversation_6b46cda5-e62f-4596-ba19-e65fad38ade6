{"id": "configuration/microservices", "title": "Microservices Configuration", "description": "Configure inter-service communication, service discovery, and distributed system patterns for your microservice architecture.", "source": "@site/docs/configuration/microservices.md", "sourceDirName": "configuration", "slug": "/configuration/microservices", "permalink": "/microgen/docs/configuration/microservices", "draft": false, "unlisted": false, "editUrl": "https://github.com/microgen-cli/microgen/tree/main/web/docs/configuration/microservices.md", "tags": [], "version": "current", "sidebarPosition": 4, "frontMatter": {"sidebar_position": 4}}