{"id": "examples/basic-api", "title": "Basic API Example", "description": "This example demonstrates how to create a simple REST API using Microservice CLI with NestJS and PostgreSQL.", "source": "@site/docs/examples/basic-api.md", "sourceDirName": "examples", "slug": "/examples/basic-api", "permalink": "/microgen/docs/examples/basic-api", "draft": false, "unlisted": false, "editUrl": "https://github.com/microgen-cli/microgen/tree/main/web/docs/examples/basic-api.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "Template Overview", "permalink": "/microgen/docs/templates/overview"}, "next": {"title": "Contributing", "permalink": "/microgen/docs/contributing"}}