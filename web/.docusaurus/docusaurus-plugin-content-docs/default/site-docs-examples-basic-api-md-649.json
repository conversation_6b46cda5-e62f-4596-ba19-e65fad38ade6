{"id": "examples/basic-api", "title": "Basic API Example", "description": "This example demonstrates how to create a simple REST API using Microservice CLI with NestJS and PostgreSQL.", "source": "@site/docs/examples/basic-api.md", "sourceDirName": "examples", "slug": "/examples/basic-api", "permalink": "/microservice-cli/docs/examples/basic-api", "draft": false, "unlisted": false, "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/docs/examples/basic-api.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "Template Overview", "permalink": "/microservice-cli/docs/templates/overview"}, "next": {"title": "Contributing", "permalink": "/microservice-cli/docs/contributing"}}