{"id": "commands/options", "title": "Command Options", "description": "This page provides a comprehensive reference for all command-line options available in Microservice CLI.", "source": "@site/docs/commands/options.md", "sourceDirName": "commands", "slug": "/commands/options", "permalink": "/microservice-cli/docs/commands/options", "draft": false, "unlisted": false, "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/docs/commands/options.md", "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"sidebar_position": 2}, "sidebar": "tutorialSidebar", "previous": {"title": "Create Command", "permalink": "/microservice-cli/docs/commands/create"}, "next": {"title": "Template Overview", "permalink": "/microservice-cli/docs/templates/overview"}}