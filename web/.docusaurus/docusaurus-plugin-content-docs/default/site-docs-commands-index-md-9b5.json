{"id": "commands/index", "title": "CLI Commands", "description": "Microgen provides a comprehensive set of commands to help you generate and manage microservice projects. This section covers all available commands and their options.", "source": "@site/docs/commands/index.md", "sourceDirName": "commands", "slug": "/commands/", "permalink": "/microgen/docs/commands/", "draft": false, "unlisted": false, "editUrl": "https://github.com/microgen-cli/microgen/tree/main/web/docs/commands/index.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}}