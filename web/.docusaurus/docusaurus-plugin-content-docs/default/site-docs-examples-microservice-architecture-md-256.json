{"id": "examples/microservice-architecture", "title": "Microservice Architecture Example", "description": "This example demonstrates how to build a complete microservice architecture using Microgen with multiple services, databases, and communication patterns.", "source": "@site/docs/examples/microservice-architecture.md", "sourceDirName": "examples", "slug": "/examples/microservice-architecture", "permalink": "/microgen/docs/examples/microservice-architecture", "draft": false, "unlisted": false, "editUrl": "https://github.com/microgen-cli/microgen/tree/main/web/docs/examples/microservice-architecture.md", "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"sidebar_position": 2}}