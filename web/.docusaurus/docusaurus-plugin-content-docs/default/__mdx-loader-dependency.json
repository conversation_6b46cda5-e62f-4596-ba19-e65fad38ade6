{"options": {"sidebarPath": "/home/<USER>/Documents/augment-projects/microservice-cli/web/sidebars.ts", "editUrl": "https://github.com/microgen-cli/microgen/tree/main/web/", "path": "docs", "editCurrentVersion": false, "editLocalizedFiles": false, "routeBasePath": "docs", "tagsBasePath": "tags", "include": ["**/*.{md,mdx}"], "exclude": ["**/_*.{js,jsx,ts,tsx,md,mdx}", "**/_*/**", "**/*.test.{js,jsx,ts,tsx}", "**/__tests__/**"], "sidebarCollapsible": true, "sidebarCollapsed": true, "docsRootComponent": "@theme/DocsRoot", "docVersionRootComponent": "@theme/DocVersionRoot", "docRootComponent": "@theme/DocRoot", "docItemComponent": "@theme/DocItem", "docTagsListComponent": "@theme/DocTagsListPage", "docTagDocListComponent": "@theme/DocTagDocListPage", "docCategoryGeneratedIndexComponent": "@theme/DocCategoryGeneratedIndexPage", "remarkPlugins": [], "rehypePlugins": [], "recmaPlugins": [], "beforeDefaultRemarkPlugins": [], "beforeDefaultRehypePlugins": [], "admonitions": true, "showLastUpdateTime": false, "showLastUpdateAuthor": false, "includeCurrentVersion": true, "disableVersioning": false, "versions": {}, "breadcrumbs": true, "onInlineTags": "warn", "id": "default"}, "versionsMetadata": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/microgen/docs", "tagsPath": "/microgen/docs/tags", "editUrl": "https://github.com/microgen-cli/microgen/tree/main/web/docs", "editUrlLocalized": "https://github.com/microgen-cli/microgen/tree/main/web/i18n/en/docusaurus-plugin-content-docs/current", "isLast": true, "routePriority": -1, "sidebarFilePath": "/home/<USER>/Documents/augment-projects/microservice-cli/web/sidebars.ts", "contentPath": "/home/<USER>/Documents/augment-projects/microservice-cli/web/docs", "contentPathLocalized": "/home/<USER>/Documents/augment-projects/microservice-cli/web/i18n/en/docusaurus-plugin-content-docs/current"}]}