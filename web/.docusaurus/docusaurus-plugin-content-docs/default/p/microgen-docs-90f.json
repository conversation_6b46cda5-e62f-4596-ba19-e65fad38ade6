{"version": {"pluginId": "default", "version": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "isLast": true, "docsSidebars": {"tutorialSidebar": [{"type": "link", "label": "Welcome to Microgen", "href": "/microgen/docs/intro", "docId": "intro", "unlisted": false}, {"type": "link", "label": "Installation", "href": "/microgen/docs/installation", "docId": "installation", "unlisted": false}, {"type": "link", "label": "Quick Start", "href": "/microgen/docs/quick-start", "docId": "quick-start", "unlisted": false}, {"type": "category", "label": "CLI Commands", "items": [{"type": "link", "label": "Create Command", "href": "/microgen/docs/commands/create", "docId": "commands/create", "unlisted": false}, {"type": "link", "label": "Command Options", "href": "/microgen/docs/commands/options", "docId": "commands/options", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Project Templates", "items": [{"type": "link", "label": "Template Overview", "href": "/microgen/docs/templates/overview", "docId": "templates/overview", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Examples", "items": [{"type": "link", "label": "Basic API Example", "href": "/microgen/docs/examples/basic-api", "docId": "examples/basic-api", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "link", "label": "Contributing", "href": "/microgen/docs/contributing", "docId": "contributing", "unlisted": false}]}, "docs": {"advanced/best-practices": {"id": "advanced/best-practices", "title": "Best Practices", "description": "Guidelines and best practices for creating high-quality Microgen templates and projects."}, "advanced/contributing-templates": {"id": "advanced/contributing-templates", "title": "Contributing Templates", "description": "Learn how to contribute templates to the Microgen community and help other developers get started faster."}, "advanced/custom-templates": {"id": "advanced/custom-templates", "title": "Custom Templates", "description": "Learn how to create and use custom templates with Microgen to standardize your team's project structure and configurations."}, "advanced/template-api": {"id": "advanced/template-api", "title": "Template API Reference", "description": "Complete API reference for creating and customizing Microgen templates."}, "advanced/template-examples": {"id": "advanced/template-examples", "title": "Template Examples", "description": "Ready-to-use template examples for common project types and organizational standards."}, "advanced/template-registry": {"id": "advanced/template-registry", "title": "Template Registry", "description": "Browse and discover templates from the Microgen community."}, "advanced/testing": {"id": "advanced/testing", "title": "Testing Templates", "description": "Learn how to test your Microgen templates to ensure they work correctly and generate valid projects."}, "commands/create": {"id": "commands/create", "title": "Create Command", "description": "The create command is the main command for generating new microservice projects. It supports both interactive and non-interactive modes with extensive customization options.", "sidebar": "tutorialSidebar"}, "commands/index": {"id": "commands/index", "title": "CLI Commands", "description": "Microgen provides a comprehensive set of commands to help you generate and manage microservice projects. This section covers all available commands and their options."}, "commands/options": {"id": "commands/options", "title": "Command Options", "description": "This page provides a comprehensive reference for all command-line options available in Microservice CLI.", "sidebar": "tutorialSidebar"}, "configuration/backend-config": {"id": "configuration/backend-config", "title": "Backend Configuration", "description": "Configure your backend services with various frameworks, databases, and microservice communication patterns."}, "configuration/frontend-config": {"id": "configuration/frontend-config", "title": "Frontend Configuration", "description": "Configure your frontend applications with modern frameworks, styling solutions, and build optimizations."}, "configuration/microservices": {"id": "configuration/microservices", "title": "Microservices Configuration", "description": "Configure inter-service communication, service discovery, and distributed system patterns for your microservice architecture."}, "configuration/project-config": {"id": "configuration/project-config", "title": "Project Configuration", "description": "Learn how to configure your Microgen projects with various settings, templates, and customization options."}, "contributing": {"id": "contributing", "title": "Contributing", "description": "We welcome contributions to Microgen! This guide will help you get started with contributing to the project.", "sidebar": "tutorialSidebar"}, "deployment/backup": {"id": "deployment/backup", "title": "Backup and Recovery", "description": "Implement comprehensive backup and disaster recovery strategies for your Microgen applications."}, "deployment/docker": {"id": "deployment/docker", "title": "Docker Deployment", "description": "Learn how to deploy your Microgen-generated projects using Docker containers for production environments."}, "deployment/monitoring": {"id": "deployment/monitoring", "title": "Monitoring and Observability", "description": "Set up comprehensive monitoring and observability for your Microgen-generated applications."}, "deployment/scaling": {"id": "deployment/scaling", "title": "Scaling Strategies", "description": "Learn how to scale your Microgen-generated applications to handle increased load and traffic."}, "deployment/security": {"id": "deployment/security", "title": "Security Hardening", "description": "Secure your Microgen-generated applications for production deployment."}, "examples/basic-api": {"id": "examples/basic-api", "title": "Basic API Example", "description": "This example demonstrates how to create a simple REST API using Microgen with NestJS and PostgreSQL.", "sidebar": "tutorialSidebar"}, "examples/full-stack-app": {"id": "examples/full-stack-app", "title": "Full Stack Application Example", "description": "This example demonstrates how to create a complete full-stack application using Microgen with a modern frontend and robust backend API."}, "examples/microservice-architecture": {"id": "examples/microservice-architecture", "title": "Microservice Architecture Example", "description": "This example demonstrates how to build a complete microservice architecture using Microgen with multiple services, databases, and communication patterns."}, "installation": {"id": "installation", "title": "Installation", "description": "Learn how to install and set up Microgen on your development environment.", "sidebar": "tutorialSidebar"}, "intro": {"id": "intro", "title": "Welcome to Microgen", "description": "Microgen is a modern command-line tool designed to accelerate the development of microservice architectures. Generate complete projects with both frontend and backend components in seconds, not hours.", "sidebar": "tutorialSidebar"}, "quick-start": {"id": "quick-start", "title": "Quick Start", "description": "Get up and running with Microgen in just 5 minutes! This guide will walk you through creating your first microservice project.", "sidebar": "tutorialSidebar"}, "templates/overview": {"id": "templates/overview", "title": "Template Overview", "description": "Microservice CLI provides three main project templates, each designed for different use cases and development scenarios. This guide explains each template and helps you choose the right one for your project.", "sidebar": "tutorialSidebar"}}}}