{"version": {"pluginId": "default", "version": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "isLast": true, "docsSidebars": {"tutorialSidebar": [{"type": "link", "label": "Welcome to Microservice CLI", "href": "/microservice-cli/docs/intro", "docId": "intro", "unlisted": false}, {"type": "link", "label": "Installation", "href": "/microservice-cli/docs/installation", "docId": "installation", "unlisted": false}, {"type": "link", "label": "Quick Start", "href": "/microservice-cli/docs/quick-start", "docId": "quick-start", "unlisted": false}, {"type": "category", "label": "CLI Commands", "items": [{"type": "link", "label": "Create Command", "href": "/microservice-cli/docs/commands/create", "docId": "commands/create", "unlisted": false}, {"type": "link", "label": "Command Options", "href": "/microservice-cli/docs/commands/options", "docId": "commands/options", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Project Templates", "items": [{"type": "link", "label": "Template Overview", "href": "/microservice-cli/docs/templates/overview", "docId": "templates/overview", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Examples", "items": [{"type": "link", "label": "Basic API Example", "href": "/microservice-cli/docs/examples/basic-api", "docId": "examples/basic-api", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "link", "label": "Contributing", "href": "/microservice-cli/docs/contributing", "docId": "contributing", "unlisted": false}]}, "docs": {"commands/create": {"id": "commands/create", "title": "Create Command", "description": "The create command is the main command for generating new microservice projects. It supports both interactive and non-interactive modes with extensive customization options.", "sidebar": "tutorialSidebar"}, "commands/options": {"id": "commands/options", "title": "Command Options", "description": "This page provides a comprehensive reference for all command-line options available in Microservice CLI.", "sidebar": "tutorialSidebar"}, "contributing": {"id": "contributing", "title": "Contributing", "description": "We welcome contributions to Microservice CLI! This guide will help you get started with contributing to the project.", "sidebar": "tutorialSidebar"}, "examples/basic-api": {"id": "examples/basic-api", "title": "Basic API Example", "description": "This example demonstrates how to create a simple REST API using Microservice CLI with NestJS and PostgreSQL.", "sidebar": "tutorialSidebar"}, "installation": {"id": "installation", "title": "Installation", "description": "Learn how to install and set up Microservice CLI on your development environment.", "sidebar": "tutorialSidebar"}, "intro": {"id": "intro", "title": "Welcome to Microservice CLI", "description": "Microservice CLI is a powerful command-line tool designed to accelerate the development of modern microservice architectures. Generate complete projects with both frontend and backend components in seconds, not hours.", "sidebar": "tutorialSidebar"}, "quick-start": {"id": "quick-start", "title": "Quick Start", "description": "Get up and running with Microservice CLI in just 5 minutes! This guide will walk you through creating your first microservice project.", "sidebar": "tutorialSidebar"}, "templates/overview": {"id": "templates/overview", "title": "Template Overview", "description": "Microservice CLI provides three main project templates, each designed for different use cases and development scenarios. This guide explains each template and helps you choose the right one for your project.", "sidebar": "tutorialSidebar"}}}}