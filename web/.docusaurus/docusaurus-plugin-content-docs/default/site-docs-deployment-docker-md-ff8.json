{"id": "deployment/docker", "title": "Docker Deployment", "description": "Learn how to deploy your Microgen-generated projects using Docker containers for production environments.", "source": "@site/docs/deployment/docker.md", "sourceDirName": "deployment", "slug": "/deployment/docker", "permalink": "/microgen/docs/deployment/docker", "draft": false, "unlisted": false, "editUrl": "https://github.com/microgen-cli/microgen/tree/main/web/docs/deployment/docker.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}}