{"id": "templates/overview", "title": "Template Overview", "description": "Microservice CLI provides three main project templates, each designed for different use cases and development scenarios. This guide explains each template and helps you choose the right one for your project.", "source": "@site/docs/templates/overview.md", "sourceDirName": "templates", "slug": "/templates/overview", "permalink": "/microservice-cli/docs/templates/overview", "draft": false, "unlisted": false, "editUrl": "https://github.com/your-org/microservice-cli/tree/main/web/docs/templates/overview.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "Command Options", "permalink": "/microservice-cli/docs/commands/options"}, "next": {"title": "Basic API Example", "permalink": "/microservice-cli/docs/examples/basic-api"}}