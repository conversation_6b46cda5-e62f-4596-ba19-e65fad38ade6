{"id": "examples/full-stack-app", "title": "Full Stack Application Example", "description": "This example demonstrates how to create a complete full-stack application using Microgen with a modern frontend and robust backend API.", "source": "@site/docs/examples/full-stack-app.md", "sourceDirName": "examples", "slug": "/examples/full-stack-app", "permalink": "/microgen/docs/examples/full-stack-app", "draft": false, "unlisted": false, "editUrl": "https://github.com/microgen-cli/microgen/tree/main/web/docs/examples/full-stack-app.md", "tags": [], "version": "current", "sidebarPosition": 3, "frontMatter": {"sidebar_position": 3}}